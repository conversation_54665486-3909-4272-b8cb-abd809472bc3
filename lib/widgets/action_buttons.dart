import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/product.dart';
import '../providers/products_provider.dart';
import '../utils/colors.dart';
import '../utils/text_styles.dart';
import '../utils/constants.dart';

class ActionButtonsColumn extends StatelessWidget {
  final Product product;
  final bool isActive;

  const ActionButtonsColumn({
    super.key,
    required this.product,
    required this.isActive,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Like button
        _buildActionButton(
          icon: product.isLiked ? Icons.favorite : Icons.favorite_border,
          iconColor: product.isLiked ? AppColors.like : AppColors.textPrimary,
          count: product.likeCount,
          onTap: () => _toggleLike(context),
        ),
        
        const SizedBox(height: AppConstants.actionButtonSpacing),
        
        // Comment button
        _buildActionButton(
          icon: Icons.chat_bubble_outline,
          iconColor: AppColors.comment,
          count: product.commentCount,
          onTap: () => _showComments(context),
        ),
        
        const SizedBox(height: AppConstants.actionButtonSpacing),
        
        // Bookmark button
        _buildActionButton(
          icon: product.isBookmarked ? Icons.bookmark : Icons.bookmark_border,
          iconColor: product.isBookmarked ? AppColors.bookmark : AppColors.textPrimary,
          onTap: () => _toggleBookmark(context),
        ),
        
        const SizedBox(height: AppConstants.actionButtonSpacing),
        
        // Share button
        _buildActionButton(
          icon: Icons.share,
          iconColor: AppColors.share,
          count: product.shareCount,
          onTap: () => _shareProduct(context),
        ),
        
        const SizedBox(height: AppConstants.actionButtonSpacing),
        
        // More options button
        _buildActionButton(
          icon: Icons.more_vert,
          iconColor: AppColors.textPrimary,
          onTap: () => _showMoreOptions(context),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color iconColor,
    required VoidCallback onTap,
    int? count,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        width: AppConstants.actionButtonSize,
        height: AppConstants.actionButtonSize,
        decoration: BoxDecoration(
          color: AppColors.overlay,
          shape: BoxShape.circle,
          border: Border.all(
            color: AppColors.border,
            width: 0.5,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: iconColor,
              size: AppConstants.actionButtonIconSize,
            ),
            if (count != null && count > 0) ...[
              const SizedBox(height: 2),
              Text(
                _formatCount(count),
                style: AppTextStyles.actionCounter.copyWith(fontSize: 10),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return count.toString();
    }
  }

  void _toggleLike(BuildContext context) {
    final productsProvider = Provider.of<ProductsProvider>(context, listen: false);
    productsProvider.toggleLike(product.id);
  }

  void _toggleBookmark(BuildContext context) {
    final productsProvider = Provider.of<ProductsProvider>(context, listen: false);
    productsProvider.toggleBookmark(product.id);
  }

  void _showComments(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.surface,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.borderRadius),
        ),
      ),
      builder: (context) => CommentsBottomSheet(product: product),
    );
  }

  void _shareProduct(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.borderRadius),
        ),
      ),
      builder: (context) => ShareBottomSheet(product: product),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.borderRadius),
        ),
      ),
      builder: (context) => MoreOptionsBottomSheet(product: product),
    );
  }
}

class CommentsBottomSheet extends StatefulWidget {
  final Product product;

  const CommentsBottomSheet({super.key, required this.product});

  @override
  State<CommentsBottomSheet> createState() => _CommentsBottomSheetState();
}

class _CommentsBottomSheetState extends State<CommentsBottomSheet> {
  final TextEditingController _commentController = TextEditingController();

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.textTertiary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          
          // Header
          Row(
            children: [
              const Text(
                'التعليقات',
                style: AppTextStyles.h4,
              ),
              const Spacer(),
              Text(
                '${widget.product.commentCount} تعليق',
                style: AppTextStyles.caption,
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Comments list
          Expanded(
            child: ListView.builder(
              itemCount: 5, // Mock comments
              itemBuilder: (context, index) => _buildCommentItem(index),
            ),
          ),
          
          // Comment input
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _commentController,
                    decoration: const InputDecoration(
                      hintText: 'اكتب تعليقاً...',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                    maxLines: null,
                  ),
                ),
                IconButton(
                  onPressed: _sendComment,
                  icon: const Icon(
                    Icons.send,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentItem(int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: AppColors.surfaceVariant,
            child: Text(
              'U${index + 1}',
              style: AppTextStyles.caption,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مستخدم ${index + 1}',
                  style: AppTextStyles.commentUsername,
                ),
                const SizedBox(height: 4),
                Text(
                  'هذا تعليق تجريبي رقم ${index + 1} على المنتج',
                  style: AppTextStyles.commentText,
                ),
                const SizedBox(height: 4),
                Text(
                  'منذ ${index + 1} ساعة',
                  style: AppTextStyles.caption,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {},
            icon: const Icon(
              Icons.favorite_border,
              size: 16,
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  void _sendComment() {
    if (_commentController.text.trim().isNotEmpty) {
      // Send comment logic here
      _commentController.clear();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال التعليق'),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }
}

class ShareBottomSheet extends StatelessWidget {
  final Product product;

  const ShareBottomSheet({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.textTertiary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'مشاركة المنتج',
            style: AppTextStyles.h4,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildShareOption(Icons.copy, 'نسخ الرابط', () {}),
              _buildShareOption(Icons.share, 'مشاركة', () {}),
              _buildShareOption(Icons.message, 'رسالة', () {}),
              _buildShareOption(Icons.email, 'إيميل', () {}),
            ],
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildShareOption(IconData icon, String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: AppColors.textPrimary),
          ),
          const SizedBox(height: 8),
          Text(label, style: AppTextStyles.caption),
        ],
      ),
    );
  }
}

class MoreOptionsBottomSheet extends StatelessWidget {
  final Product product;

  const MoreOptionsBottomSheet({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.textTertiary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          _buildOption(Icons.info_outline, 'تفاصيل المنتج', () {}),
          _buildOption(Icons.report_outlined, 'إبلاغ', () {}),
          _buildOption(Icons.block_outlined, 'حظر المتجر', () {}),
          _buildOption(Icons.not_interested_outlined, 'غير مهتم', () {}),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildOption(IconData icon, String title, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: AppColors.textSecondary),
      title: Text(title, style: AppTextStyles.bodyMedium),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }
}
