import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/comment.dart';
import '../models/product.dart';
import '../utils/colors.dart';
import '../utils/text_styles.dart';
import '../utils/constants.dart';
import 'shimmer_widgets.dart';

class CommentsBottomSheet extends StatefulWidget {
  final Product product;

  const CommentsBottomSheet({super.key, required this.product});

  @override
  State<CommentsBottomSheet> createState() => _CommentsBottomSheetState();
}

class _CommentsBottomSheetState extends State<CommentsBottomSheet> {
  final TextEditingController _commentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  List<Comment> _comments = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  Comment? _replyingTo;
  String _sortBy = 'newest'; // newest, oldest, most_liked

  @override
  void initState() {
    super.initState();
    _loadComments();
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadComments() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _comments = CommentMockData.generateMockComments(widget.product.id);
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildSortOptions(),
          Expanded(
            child: _buildCommentsList(),
          ),
          _buildCommentInput(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.textTertiary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          
          // Header row
          Row(
            children: [
              Text(
                'التعليقات',
                style: AppTextStyles.h4,
              ),
              const Spacer(),
              Text(
                '${_comments.length} تعليق',
                style: AppTextStyles.caption,
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(
                  Icons.close,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSortOptions() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          _buildSortChip('الأحدث', 'newest'),
          const SizedBox(width: 8),
          _buildSortChip('الأقدم', 'oldest'),
          const SizedBox(width: 8),
          _buildSortChip('الأكثر إعجاباً', 'most_liked'),
        ],
      ),
    );
  }

  Widget _buildSortChip(String label, String value) {
    final isSelected = _sortBy == value;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _sortBy = value;
        });
        _sortComments();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          label,
          style: AppTextStyles.caption.copyWith(
            color: isSelected ? AppColors.textOnPrimary : AppColors.textSecondary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildCommentsList() {
    if (_isLoading) {
      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 5,
        itemBuilder: (context, index) => const CommentItemShimmer(),
      );
    }

    if (_comments.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _comments.length,
      itemBuilder: (context, index) {
        final comment = _comments[index];
        return CommentItem(
          comment: comment,
          onReply: (comment) => _startReply(comment),
          onLike: (comment) => _toggleCommentLike(comment),
          onReport: (comment) => _reportComment(comment),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد تعليقات بعد',
            style: AppTextStyles.h5,
          ),
          const SizedBox(height: 8),
          Text(
            'كن أول من يعلق على هذا المنتج',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(color: AppColors.border, width: 0.5),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            if (_replyingTo != null) _buildReplyingToIndicator(),
            Row(
              children: [
                // User avatar
                CircleAvatar(
                  radius: 16,
                  backgroundColor: AppColors.primary,
                  child: Text(
                    'أ',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Input field
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppColors.surfaceVariant,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _commentController,
                            decoration: InputDecoration(
                              hintText: _replyingTo != null 
                                  ? 'رد على ${_replyingTo!.username}...'
                                  : 'اكتب تعليقاً...',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.zero,
                            ),
                            maxLines: null,
                            maxLength: 500,
                            buildCounter: (context, {required currentLength, required isFocused, maxLength}) {
                              return null; // Hide counter
                            },
                          ),
                        ),
                        IconButton(
                          onPressed: _sendComment,
                          icon: Icon(
                            Icons.send,
                            color: _commentController.text.trim().isNotEmpty 
                                ? AppColors.primary 
                                : AppColors.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReplyingToIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.reply,
            size: 16,
            color: AppColors.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'رد على ${_replyingTo!.username}',
              style: AppTextStyles.caption.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                _replyingTo = null;
              });
            },
            child: Icon(
              Icons.close,
              size: 16,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  void _sortComments() {
    setState(() {
      switch (_sortBy) {
        case 'newest':
          _comments.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          break;
        case 'oldest':
          _comments.sort((a, b) => a.createdAt.compareTo(b.createdAt));
          break;
        case 'most_liked':
          _comments.sort((a, b) => b.likeCount.compareTo(a.likeCount));
          break;
      }
    });
  }

  void _startReply(Comment comment) {
    setState(() {
      _replyingTo = comment;
    });
    _commentController.clear();
  }

  void _toggleCommentLike(Comment comment) {
    setState(() {
      final index = _comments.indexWhere((c) => c.id == comment.id);
      if (index != -1) {
        _comments[index] = _comments[index].toggleLike();
      }
    });
    
    HapticFeedback.lightImpact();
  }

  void _reportComment(Comment comment) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إبلاغ عن التعليق',
              style: AppTextStyles.h5,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.report, color: AppColors.error),
              title: const Text('محتوى غير لائق'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading:  Icon(Icons.accessibility, color: AppColors.error),
              title: const Text('رسائل مزعجة'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.block, color: AppColors.error),
              title: const Text('حظر المستخدم'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _sendComment() {
    final text = _commentController.text.trim();
    if (text.isEmpty) return;

    final newComment = Comment(
      id: 'comment_${DateTime.now().millisecondsSinceEpoch}',
      productId: widget.product.id,
      userId: 'current_user',
      username: 'أنت',
      userAvatar: null,
      text: text,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      likeCount: 0,
      isLiked: false,
      isVerified: false,
      parentCommentId: _replyingTo?.id,
      replies: [],
      isPinned: false,
      isEdited: false,
    );

    setState(() {
      if (_replyingTo != null) {
        // Add as reply
        final parentIndex = _comments.indexWhere((c) => c.id == _replyingTo!.id);
        if (parentIndex != -1) {
          _comments[parentIndex] = _comments[parentIndex].addReply(newComment);
        }
        _replyingTo = null;
      } else {
        // Add as new comment
        _comments.insert(0, newComment);
      }
    });

    _commentController.clear();
    HapticFeedback.lightImpact();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_replyingTo != null ? 'تم إرسال الرد' : 'تم إرسال التعليق'),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}

class CommentItem extends StatefulWidget {
  final Comment comment;
  final Function(Comment) onReply;
  final Function(Comment) onLike;
  final Function(Comment) onReport;

  const CommentItem({
    super.key,
    required this.comment,
    required this.onReply,
    required this.onLike,
    required this.onReport,
  });

  @override
  State<CommentItem> createState() => _CommentItemState();
}

class _CommentItemState extends State<CommentItem> {
  bool _showReplies = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMainComment(),
          if (widget.comment.hasReplies) _buildRepliesSection(),
        ],
      ),
    );
  }

  Widget _buildMainComment() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: widget.comment.isPinned
            ? AppColors.primary.withOpacity(0.05)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: widget.comment.isPinned
            ? Border.all(color: AppColors.primary.withOpacity(0.2))
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.comment.isPinned) _buildPinnedIndicator(),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildUserAvatar(),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildUserInfo(),
                    const SizedBox(height: 4),
                    _buildCommentText(),
                    const SizedBox(height: 8),
                    _buildCommentActions(),
                  ],
                ),
              ),
              _buildMoreButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPinnedIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            Icons.push_pin,
            size: 14,
            color: AppColors.primary,
          ),
          const SizedBox(width: 4),
          Text(
            'تعليق مثبت',
            style: AppTextStyles.caption.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserAvatar() {
    return Stack(
      children: [
        CircleAvatar(
          radius: 18,
          backgroundColor: AppColors.surfaceVariant,
          backgroundImage: widget.comment.hasAvatar
              ? CachedNetworkImageProvider(widget.comment.userAvatar!)
              : null,
          child: !widget.comment.hasAvatar
              ? Text(
                  widget.comment.userInitials,
                  style: AppTextStyles.caption.copyWith(fontSize: 12),
                )
              : null,
        ),
        if (widget.comment.isVerified)
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: 14,
              height: 14,
              decoration: const BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.verified,
                size: 10,
                color: AppColors.textOnPrimary,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildUserInfo() {
    return Row(
      children: [
        Text(
          widget.comment.username,
          style: AppTextStyles.commentUsername,
        ),
        const SizedBox(width: 8),
        Text(
          widget.comment.timeAgo,
          style: AppTextStyles.caption,
        ),
        if (widget.comment.isEdited) ...[
          const SizedBox(width: 8),
          Text(
            '(معدل)',
            style: AppTextStyles.caption.copyWith(
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCommentText() {
    return Text(
      widget.comment.text,
      style: AppTextStyles.commentText,
    );
  }

  Widget _buildCommentActions() {
    return Row(
      children: [
        _buildActionButton(
          icon: widget.comment.isLiked ? Icons.favorite : Icons.favorite_border,
          label: widget.comment.likeCount > 0 ? widget.comment.formattedLikeCount : 'إعجاب',
          color: widget.comment.isLiked ? AppColors.like : AppColors.textTertiary,
          onTap: () => widget.onLike(widget.comment),
        ),
        const SizedBox(width: 16),
        _buildActionButton(
          icon: Icons.reply,
          label: 'رد',
          color: AppColors.textTertiary,
          onTap: () => widget.onReply(widget.comment),
        ),
        if (widget.comment.hasReplies) ...[
          const SizedBox(width: 16),
          _buildActionButton(
            icon: _showReplies ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
            label: '${widget.comment.totalReplies} ${widget.comment.totalReplies == 1 ? 'رد' : 'ردود'}',
            color: AppColors.textTertiary,
            onTap: () {
              setState(() {
                _showReplies = !_showReplies;
              });
            },
          ),
        ],
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: AppTextStyles.caption.copyWith(color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildMoreButton() {
    return GestureDetector(
      onTap: () => widget.onReport(widget.comment),
      child: const Icon(
        Icons.more_vert,
        size: 16,
        color: AppColors.textTertiary,
      ),
    );
  }

  Widget _buildRepliesSection() {
    if (!_showReplies) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 8, right: 30),
      child: Column(
        children: widget.comment.replies.map((reply) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 14,
                  backgroundColor: AppColors.surfaceVariant,
                  backgroundImage: reply.hasAvatar
                      ? CachedNetworkImageProvider(reply.userAvatar!)
                      : null,
                  child: !reply.hasAvatar
                      ? Text(
                          reply.userInitials,
                          style: AppTextStyles.caption.copyWith(fontSize: 10),
                        )
                      : null,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            reply.username,
                            style: AppTextStyles.commentUsername.copyWith(fontSize: 12),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            reply.timeAgo,
                            style: AppTextStyles.caption.copyWith(fontSize: 10),
                          ),
                        ],
                      ),
                      const SizedBox(height: 2),
                      Text(
                        reply.text,
                        style: AppTextStyles.commentText.copyWith(fontSize: 12),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          GestureDetector(
                            onTap: () => widget.onLike(reply),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  reply.isLiked ? Icons.favorite : Icons.favorite_border,
                                  size: 12,
                                  color: reply.isLiked ? AppColors.like : AppColors.textTertiary,
                                ),
                                if (reply.likeCount > 0) ...[
                                  const SizedBox(width: 2),
                                  Text(
                                    reply.formattedLikeCount,
                                    style: AppTextStyles.caption.copyWith(fontSize: 10),
                                  ),
                                ],
                              ],
                            ),
                          ),
                          const SizedBox(width: 12),
                          GestureDetector(
                            onTap: () => widget.onReply(reply),
                            child: Text(
                              'رد',
                              style: AppTextStyles.caption.copyWith(
                                fontSize: 10,
                                color: AppColors.textTertiary,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}

class CommentItemShimmer extends StatelessWidget {
  const CommentItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ShimmerLoading(
      isLoading: true,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: const BoxDecoration(
                color: AppColors.surfaceVariant,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 80,
                        height: 14,
                        decoration: BoxDecoration(
                          color: AppColors.surfaceVariant,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        width: 60,
                        height: 12,
                        decoration: BoxDecoration(
                          color: AppColors.surfaceVariant,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    height: 14,
                    decoration: BoxDecoration(
                      color: AppColors.surfaceVariant,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    width: 200,
                    height: 14,
                    decoration: BoxDecoration(
                      color: AppColors.surfaceVariant,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        width: 50,
                        height: 12,
                        decoration: BoxDecoration(
                          color: AppColors.surfaceVariant,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Container(
                        width: 30,
                        height: 12,
                        decoration: BoxDecoration(
                          color: AppColors.surfaceVariant,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
