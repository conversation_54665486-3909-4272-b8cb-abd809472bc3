import 'package:flutter/material.dart';
import 'package:badges/badges.dart' as badges;
import 'package:provider/provider.dart';
import '../utils/colors.dart';
import '../utils/text_styles.dart';
import '../utils/constants.dart';
import '../providers/cart_provider.dart';

class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppConstants.bottomNavHeight,
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(
            color: AppColors.border,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(
              context: context,
              index: 0,
              icon: Icons.home_outlined,
              activeIcon: Icons.home,
              label: 'الرئيسية',
            ),
            _buildNavItem(
              context: context,
              index: 1,
              icon: Icons.explore_outlined,
              activeIcon: Icons.explore,
              label: 'اكتشف',
            ),
            _buildNavItem(
              context: context,
              index: 2,
              icon: Icons.shopping_cart_outlined,
              activeIcon: Icons.shopping_cart,
              label: 'السلة',
              showBadge: true,
            ),
            _buildNavItem(
              context: context,
              index: 3,
              icon: Icons.chat_bubble_outline,
              activeIcon: Icons.chat_bubble,
              label: 'الرسائل',
              showBadge: true,
              badgeCount: 3, // Mock notification count
            ),
            _buildNavItem(
              context: context,
              index: 4,
              icon: Icons.person_outline,
              activeIcon: Icons.person,
              label: 'الملف الشخصي',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
    bool showBadge = false,
    int? badgeCount,
  }) {
    final isActive = currentIndex == index;
    final cartProvider = Provider.of<CartProvider>(context);
    
    // Determine badge count for cart
    int? displayBadgeCount = badgeCount;
    if (index == 2 && showBadge) {
      displayBadgeCount = cartProvider.itemCount > 0 ? cartProvider.itemCount : null;
    }

    Widget iconWidget = Icon(
      isActive ? activeIcon : icon,
      color: isActive ? AppColors.primary : AppColors.textSecondary,
      size: AppConstants.bottomNavIconSize,
    );

    if (showBadge && displayBadgeCount != null && displayBadgeCount > 0) {
      iconWidget = badges.Badge(
        badgeContent: Text(
          displayBadgeCount > 99 ? '99+' : displayBadgeCount.toString(),
          style: AppTextStyles.actionCounter.copyWith(
            fontSize: 10,
            color: AppColors.textOnPrimary,
          ),
        ),
        badgeStyle: const badges.BadgeStyle(
          badgeColor: AppColors.primary,
          padding: EdgeInsets.all(4),
        ),
        position: badges.BadgePosition.topEnd(top: -8, end: -8),
        child: iconWidget,
      );
    }

    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingSmall,
          vertical: AppConstants.paddingSmall,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            iconWidget,
            const SizedBox(height: 4),
            Text(
              label,
              style: isActive 
                  ? AppTextStyles.navLabelActive 
                  : AppTextStyles.navLabel,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class BottomNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final bool showBadge;
  final int? badgeCount;

  const BottomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    this.showBadge = false,
    this.badgeCount,
  });
}

// Custom bottom navigation with floating action button style
class TikTokStyleBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const TikTokStyleBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppConstants.bottomNavHeight,
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: const Border(
          top: BorderSide(
            color: AppColors.border,
            width: 0.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.overlay,
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildNavItem(
              context: context,
              index: 0,
              icon: Icons.home_outlined,
              activeIcon: Icons.home,
              label: 'الرئيسية',
            ),
            _buildNavItem(
              context: context,
              index: 1,
              icon: Icons.explore_outlined,
              activeIcon: Icons.explore,
              label: 'اكتشف',
            ),
            _buildCenterButton(context),
            _buildNavItem(
              context: context,
              index: 3,
              icon: Icons.chat_bubble_outline,
              activeIcon: Icons.chat_bubble,
              label: 'الرسائل',
              showBadge: true,
              badgeCount: 3,
            ),
            _buildNavItem(
              context: context,
              index: 4,
              icon: Icons.person_outline,
              activeIcon: Icons.person,
              label: 'الملف الشخصي',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCenterButton(BuildContext context) {
    final cartProvider = Provider.of<CartProvider>(context);
    
    return GestureDetector(
      onTap: () => onTap(2),
      child: Container(
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          gradient: AppColors.primaryGradient,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            const Icon(
              Icons.shopping_cart,
              color: AppColors.textOnPrimary,
              size: 28,
            ),
            if (cartProvider.itemCount > 0)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: AppColors.accent,
                    shape: BoxShape.circle,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    cartProvider.itemCount > 99 ? '99+' : cartProvider.itemCount.toString(),
                    style: AppTextStyles.actionCounter.copyWith(
                      fontSize: 10,
                      color: AppColors.textOnPrimary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
    bool showBadge = false,
    int? badgeCount,
  }) {
    final isActive = currentIndex == index;

    Widget iconWidget = Icon(
      isActive ? activeIcon : icon,
      color: isActive ? AppColors.primary : AppColors.textSecondary,
      size: 24,
    );

    if (showBadge && badgeCount != null && badgeCount > 0) {
      iconWidget = badges.Badge(
        badgeContent: Text(
          badgeCount > 99 ? '99+' : badgeCount.toString(),
          style: AppTextStyles.actionCounter.copyWith(
            fontSize: 10,
            color: AppColors.textOnPrimary,
          ),
        ),
        badgeStyle: const badges.BadgeStyle(
          badgeColor: AppColors.primary,
          padding: EdgeInsets.all(4),
        ),
        position: badges.BadgePosition.topEnd(top: -8, end: -8),
        child: iconWidget,
      );
    }

    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            iconWidget,
            const SizedBox(height: 4),
            Text(
              label,
              style: AppTextStyles.navLabel.copyWith(
                color: isActive ? AppColors.primary : AppColors.textSecondary,
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
