import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../utils/colors.dart';

class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;
  final bool isActive;
  final bool autoPlay;
  final bool showControls;

  const VideoPlayerWidget({
    super.key,
    required this.videoUrl,
    required this.isActive,
    this.autoPlay = true,
    this.showControls = false,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showPlayButton = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void didUpdateWidget(VideoPlayerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.videoUrl != widget.videoUrl) {
      _disposeController();
      _initializeVideo();
    }
    
    if (oldWidget.isActive != widget.isActive) {
      _handleActiveStateChange();
    }
  }

  @override
  void dispose() {
    _disposeController();
    super.dispose();
  }

  void _initializeVideo() {
    try {
      _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
      _controller!.initialize().then((_) {
        if (mounted) {
          setState(() {
            _isInitialized = true;
            _hasError = false;
          });
          
          if (widget.isActive && widget.autoPlay) {
            _playVideo();
          }
        }
      }).catchError((error) {
        if (mounted) {
          setState(() {
            _hasError = true;
            _isInitialized = false;
          });
        }
      });

      _controller!.addListener(_videoListener);
      _controller!.setLooping(true);
    } catch (e) {
      setState(() {
        _hasError = true;
        _isInitialized = false;
      });
    }
  }

  void _disposeController() {
    _controller?.removeListener(_videoListener);
    _controller?.dispose();
    _controller = null;
  }

  void _videoListener() {
    if (_controller != null && mounted) {
      final isPlaying = _controller!.value.isPlaying;
      if (_isPlaying != isPlaying) {
        setState(() {
          _isPlaying = isPlaying;
        });
      }
    }
  }

  void _handleActiveStateChange() {
    if (!_isInitialized || _controller == null) return;

    if (widget.isActive) {
      if (widget.autoPlay) {
        _playVideo();
      }
    } else {
      _pauseVideo();
    }
  }

  void _playVideo() {
    if (_controller != null && _isInitialized && !_hasError) {
      _controller!.play();
      setState(() {
        _showPlayButton = false;
      });
    }
  }

  void _pauseVideo() {
    if (_controller != null && _isInitialized) {
      _controller!.pause();
      setState(() {
        _showPlayButton = true;
      });
    }
  }

  void _togglePlayPause() {
    if (_controller == null || !_isInitialized || _hasError) return;

    if (_controller!.value.isPlaying) {
      _pauseVideo();
    } else {
      _playVideo();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _togglePlayPause,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: AppColors.background,
        child: Stack(
          children: [
            // Video player
            if (_isInitialized && !_hasError)
              Center(
                child: AspectRatio(
                  aspectRatio: _controller!.value.aspectRatio,
                  child: VideoPlayer(_controller!),
                ),
              ),

            // Loading indicator
            if (!_isInitialized && !_hasError)
              const Center(
                child: CircularProgressIndicator(
                  color: AppColors.primary,
                ),
              ),

            // Error state
            if (_hasError)
              _buildErrorState(),

            // Play button overlay
            if (_showPlayButton && _isInitialized && !_hasError)
              _buildPlayButtonOverlay(),

            // Video controls (if enabled)
            if (widget.showControls && _isInitialized && !_hasError)
              _buildVideoControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.surfaceVariant,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: AppColors.error,
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              'خطأ في تحميل الفيديو',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayButtonOverlay() {
    return Center(
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: AppColors.overlay,
          shape: BoxShape.circle,
          border: Border.all(
            color: AppColors.textPrimary,
            width: 2,
          ),
        ),
        child: const Icon(
          Icons.play_arrow,
          color: AppColors.textPrimary,
          size: 40,
        ),
      ),
    );
  }

  Widget _buildVideoControls() {
    return Positioned(
      bottom: 20,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.overlay,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // Play/Pause button
            GestureDetector(
              onTap: _togglePlayPause,
              child: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: AppColors.textPrimary,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            
            // Progress bar
            Expanded(
              child: VideoProgressIndicator(
                _controller!,
                allowScrubbing: true,
                colors: const VideoProgressColors(
                  playedColor: AppColors.primary,
                  bufferedColor: AppColors.textTertiary,
                  backgroundColor: AppColors.surfaceVariant,
                ),
              ),
            ),
            const SizedBox(width: 12),
            
            // Duration
            Text(
              _formatDuration(_controller!.value.duration),
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

// Custom video progress indicator with better styling
class CustomVideoProgressIndicator extends StatelessWidget {
  final VideoPlayerController controller;
  final bool allowScrubbing;

  const CustomVideoProgressIndicator({
    super.key,
    required this.controller,
    this.allowScrubbing = true,
  });

  @override
  Widget build(BuildContext context) {
    return VideoProgressIndicator(
      controller,
      allowScrubbing: allowScrubbing,
      colors: const VideoProgressColors(
        playedColor: AppColors.primary,
        bufferedColor: AppColors.textTertiary,
        backgroundColor: AppColors.surfaceVariant,
      ),
    );
  }
}

// Video thumbnail widget for preview
class VideoThumbnail extends StatelessWidget {
  final String videoUrl;
  final double? width;
  final double? height;
  final VoidCallback? onTap;

  const VideoThumbnail({
    super.key,
    required this.videoUrl,
    this.width,
    this.height,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            const Icon(
              Icons.play_circle_outline,
              color: AppColors.textPrimary,
              size: 48,
            ),
            Positioned(
              bottom: 8,
              right: 8,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.overlay,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.videocam,
                      color: AppColors.textPrimary,
                      size: 12,
                    ),
                    SizedBox(width: 2),
                    Text(
                      'فيديو',
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
