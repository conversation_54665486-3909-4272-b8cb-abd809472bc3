import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../models/product.dart';
import '../providers/cart_provider.dart';
import '../providers/products_provider.dart';
import '../utils/colors.dart';
import '../utils/text_styles.dart';
import '../utils/constants.dart';
import 'action_buttons.dart';
import 'video_player_widget.dart';

class TikTokStyleProductCard extends StatefulWidget {
  final Product product;
  final bool isActive;

  const TikTokStyleProductCard({
    super.key,
    required this.product,
    required this.isActive,
  });

  @override
  State<TikTokStyleProductCard> createState() => _TikTokStyleProductCardState();
}

class _TikTokStyleProductCardState extends State<TikTokStyleProductCard> {
  bool _showProductInfo = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.background,
      child: Stack(
        children: [
          // Background media (video or image)
          _buildBackgroundMedia(),
          
          // Gradient overlay
          _buildGradientOverlay(),
          
          // Action buttons (right side)
          Positioned(
            right: 16,
            bottom: 100,
            child: ActionButtonsColumn(
              product: widget.product,
              isActive: widget.isActive,
            ),
          ),
          
          // Product info (bottom left)
          Positioned(
            left: 16,
            right: 80,
            bottom: 100,
            child: _buildProductInfo(),
          ),
          
          // Top overlay with seller info
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: _buildTopOverlay(),
          ),
        ],
      ),
    );
  }

  Widget _buildBackgroundMedia() {
    if (widget.product.hasVideo) {
      return VideoPlayerWidget(
        videoUrl: widget.product.videoUrl!,
        isActive: widget.isActive,
      );
    } else {
      return _buildImageBackground();
    }
  }

  Widget _buildImageBackground() {
    return GestureDetector(
      onTap: () => setState(() => _showProductInfo = !_showProductInfo),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: CachedNetworkImage(
          imageUrl: widget.product.primaryImage,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: AppColors.surfaceVariant,
            child: const Center(
              child: CircularProgressIndicator(
                color: AppColors.primary,
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: AppColors.surfaceVariant,
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                color: AppColors.textTertiary,
                size: 64,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGradientOverlay() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.transparent,
            AppColors.background.withOpacity(0.3),
            AppColors.background.withOpacity(0.8),
          ],
          stops: const [0.0, 0.5, 0.8, 1.0],
        ),
      ),
    );
  }

  Widget _buildTopOverlay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.background.withOpacity(0.6),
            Colors.transparent,
          ],
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Seller avatar
            CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.surfaceVariant,
              backgroundImage: widget.product.sellerAvatar != null
                  ? CachedNetworkImageProvider(widget.product.sellerAvatar!)
                  : null,
              child: widget.product.sellerAvatar == null
                  ? Text(
                      widget.product.sellerName[0].toUpperCase(),
                      style: AppTextStyles.username.copyWith(fontSize: 16),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            
            // Seller info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.product.sellerName,
                    style: AppTextStyles.username,
                  ),
                  Text(
                    widget.product.category,
                    style: AppTextStyles.caption,
                  ),
                ],
              ),
            ),
            
            // Follow button
            _buildFollowButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildFollowButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.circular(20),
      ),
      child:  Text(
        'متابعة',
        style: AppTextStyles.buttonSmall,
      ),
    );
  }

  Widget _buildProductInfo() {
    return AnimatedContainer(
      duration: AppConstants.mediumAnimation,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Product title
          Text(
            widget.product.title,
            style: AppTextStyles.productTitle,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          
          // Price and rating
          Row(
            children: [
              Text(
                widget.product.formattedPrice,
                style: AppTextStyles.price,
              ),
              if (widget.product.hasDiscount) ...[
                const SizedBox(width: 8),
                Text(
                  widget.product.formattedOriginalPrice!,
                  style: AppTextStyles.priceOriginal,
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.discount,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    widget.product.formattedDiscountPercentage,
                    style: AppTextStyles.discount.copyWith(fontSize: 12),
                  ),
                ),
              ],
              const Spacer(),
              const Icon(
                Icons.star,
                color: Colors.amber,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                widget.product.formattedRating,
                style: AppTextStyles.rating,
              ),
            ],
          ),
          
          if (_showProductInfo) ...[
            const SizedBox(height: 12),
            _buildExpandedInfo(),
          ],
          
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildExpandedInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.product.description,
            style: AppTextStyles.bodySmall,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: widget.product.tags.take(3).map((tag) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '#$tag',
                style: AppTextStyles.caption.copyWith(
                  color: AppColors.primary,
                ),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _addToCart(),
            icon: const Icon(Icons.shopping_cart, size: 20),
            label: const Text('أضف للسلة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.addToCart,
              foregroundColor: AppColors.textOnPrimary,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _buyNow(),
            icon: const Icon(Icons.flash_on, size: 20),
            label: const Text('اشتري الآن'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buyNow,
              foregroundColor: AppColors.textOnPrimary,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _addToCart() {
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    cartProvider.addToCart(widget.product);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppConstants.successAddedToCart),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _buyNow() {
    // Navigate to checkout with this product
    debugPrint('Buy now: ${widget.product.title}');
  }
}
