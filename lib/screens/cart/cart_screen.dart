import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/cart_provider.dart';
import '../../utils/colors.dart';
import '../../utils/text_styles.dart';
import '../../utils/constants.dart';
import '../../widgets/product_card.dart';
import '../../widgets/shimmer_widgets.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> 
    with AutomaticKeepAliveClientMixin {
  
  final TextEditingController _couponController = TextEditingController();

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Safe<PERSON>rea(
        child: Consumer<CartProvider>(
          builder: (context, cartProvider, child) {
            if (cartProvider.isEmpty) {
              return _buildEmptyCart();
            }

            return Column(
              children: [
                _buildHeader(cartProvider),
                Expanded(
                  child: _buildCartContent(cartProvider),
                ),
                _buildCheckoutSection(cartProvider),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader(CartProvider cartProvider) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        children: [
           Text(
            'سلة التسوق',
            style: AppTextStyles.h3,
          ),
          const Spacer(),
          Text(
            '${cartProvider.itemCount} منتج',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(width: 16),
          IconButton(
            onPressed: () => _showClearCartDialog(cartProvider),
            icon: const Icon(
              Icons.delete_outline,
              color: AppColors.error,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyCart() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.shopping_cart_outlined,
                size: 64,
                color: AppColors.textTertiary,
              ),
            ),
            const SizedBox(height: 24),
             Text(
              'سلة التسوق فارغة',
              style: AppTextStyles.h4,
            ),
            const SizedBox(height: 8),
             Text(
              'ابدأ بإضافة المنتجات المفضلة لديك',
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate back to home or discover
              },
              icon: const Icon(Icons.shopping_bag),
              label: const Text('تسوق الآن'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartContent(CartProvider cartProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: Column(
        children: [
          // Cart items
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: cartProvider.cart.items.length,
            itemBuilder: (context, index) {
              final item = cartProvider.cart.items[index];
              return _buildCartItem(item, cartProvider);
            },
          ),
          
          const SizedBox(height: 24),
          
          // Coupon section
          _buildCouponSection(cartProvider),
          
          const SizedBox(height: 24),
          
          // Order summary
          _buildOrderSummary(cartProvider),
          
          const SizedBox(height: 100), // Space for checkout button
        ],
      ),
    );
  }

  Widget _buildCartItem(dynamic item, CartProvider cartProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Row(
        children: [
          // Product image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 80,
              height: 80,
              color: AppColors.surfaceVariant,
              child: const Icon(
                Icons.image,
                color: AppColors.textTertiary,
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Product details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.title,
                  style: AppTextStyles.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  item.formattedUnitPrice,
                  style: AppTextStyles.price.copyWith(fontSize: 16),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    // Quantity controls
                    _buildQuantityControls(item, cartProvider),
                    const Spacer(),
                    // Remove button
                    IconButton(
                      onPressed: () => cartProvider.removeFromCart(item.id),
                      icon: const Icon(
                        Icons.delete_outline,
                        color: AppColors.error,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityControls(dynamic item, CartProvider cartProvider) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: item.quantity > 1 
                ? () => cartProvider.updateQuantity(item.id, item.quantity - 1)
                : null,
            icon: const Icon(Icons.remove, size: 16),
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(
              item.quantity.toString(),
              style: AppTextStyles.bodyMedium,
            ),
          ),
          IconButton(
            onPressed: () => cartProvider.updateQuantity(item.id, item.quantity + 1),
            icon: const Icon(Icons.add, size: 16),
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
        ],
      ),
    );
  }

  Widget _buildCouponSection(CartProvider cartProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
           Text(
            'كود الخصم',
            style: AppTextStyles.label,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _couponController,
                  decoration: const InputDecoration(
                    hintText: 'أدخل كود الخصم',
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: cartProvider.isLoading 
                    ? null 
                    : () => _applyCoupon(cartProvider),
                child: cartProvider.isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('تطبيق'),
              ),
            ],
          ),
          if (cartProvider.cart.hasCoupon) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: AppColors.success,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'تم تطبيق كود الخصم: ${cartProvider.cart.couponCode}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.success,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => cartProvider.removeCoupon(),
                    icon: const Icon(
                      Icons.close,
                      size: 16,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderSummary(CartProvider cartProvider) {
    final cart = cartProvider.cart;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
           Text(
            'ملخص الطلب',
            style: AppTextStyles.label,
          ),
          const SizedBox(height: 16),
          _buildSummaryRow('المجموع الفرعي', cart.formattedSubtotal),
          if (cart.hasDiscount)
            _buildSummaryRow('الخصم', '-${cart.formattedDiscount}', 
                color: AppColors.success),
          _buildSummaryRow('الضريبة', cart.formattedTax),
          if (cart.hasShipping)
            _buildSummaryRow('الشحن', cart.formattedShipping),
          const Divider(height: 24),
          _buildSummaryRow('المجموع الكلي', cart.formattedTotal, 
              isTotal: true),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, 
      {Color? color, bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal ? AppTextStyles.label : AppTextStyles.bodyMedium,
          ),
          Text(
            value,
            style: isTotal 
                ? AppTextStyles.price 
                : AppTextStyles.bodyMedium.copyWith(color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckoutSection(CartProvider cartProvider) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(color: AppColors.border, width: 0.5),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                     Text(
                      'المجموع الكلي',
                      style: AppTextStyles.bodySmall,
                    ),
                    Text(
                      cartProvider.cart.formattedTotal,
                      style: AppTextStyles.price,
                    ),
                  ],
                ),
                const Spacer(),
                ElevatedButton(
                  onPressed: () => _proceedToCheckout(cartProvider),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                  ),
                  child: const Text('إتمام الطلب'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _applyCoupon(CartProvider cartProvider) {
    final couponCode = _couponController.text.trim();
    if (couponCode.isNotEmpty) {
      cartProvider.applyCoupon(couponCode);
    }
  }

  void _proceedToCheckout(CartProvider cartProvider) {
    // Navigate to checkout screen
    debugPrint('Proceeding to checkout with ${cartProvider.itemCount} items');
  }

  void _showClearCartDialog(CartProvider cartProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surface,
        title: const Text('مسح السلة'),
        content: const Text('هل أنت متأكد من مسح جميع المنتجات من السلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              cartProvider.clearCart();
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }
}
