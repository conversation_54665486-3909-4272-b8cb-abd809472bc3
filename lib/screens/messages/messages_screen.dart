import 'package:flutter/material.dart';
import '../../utils/colors.dart';
import '../../utils/text_styles.dart';
import '../../utils/constants.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({super.key});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> 
    with AutomaticKeepAliveClientMixin {
  
  final TextEditingController _searchController = TextEditingController();
  final List<Map<String, dynamic>> _mockMessages = [
    {
      'id': '1',
      'name': 'متجر الإلكترونيات',
      'avatar': null,
      'lastMessage': 'شكراً لك على الطلب! سيتم الشحن خلال 24 ساعة',
      'time': 'منذ 5 دقائق',
      'unreadCount': 2,
      'isOnline': true,
    },
    {
      'id': '2',
      'name': 'متجر الأزياء العصرية',
      'avatar': null,
      'lastMessage': 'هل تحتاج مساعدة في اختيار المقاس؟',
      'time': 'منذ ساعة',
      'unreadCount': 0,
      'isOnline': false,
    },
    {
      'id': '3',
      'name': 'خدمة العملاء',
      'avatar': null,
      'lastMessage': 'مرحباً! كيف يمكننا مساعدتك اليوم؟',
      'time': 'أمس',
      'unreadCount': 1,
      'isOnline': true,
    },
  ];

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            _buildSearchBar(),
            Expanded(
              child: _buildMessagesList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        children: [
          const Text(
            'الرسائل',
            style: AppTextStyles.h3,
          ),
          const Spacer(),
          IconButton(
            onPressed: () => _showNewMessageDialog(),
            icon: const Icon(
              Icons.edit,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          hintText: 'ابحث في الرسائل...',
          prefixIcon: Icon(Icons.search),
        ),
        onChanged: (value) {
          // Implement search functionality
        },
      ),
    );
  }

  Widget _buildMessagesList() {
    if (_mockMessages.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _mockMessages.length,
      itemBuilder: (context, index) {
        final message = _mockMessages[index];
        return _buildMessageItem(message);
      },
    );
  }

  Widget _buildMessageItem(Map<String, dynamic> message) {
    return GestureDetector(
      onTap: () => _openChat(message),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        child: Row(
          children: [
            // Avatar
            Stack(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: AppColors.surfaceVariant,
                  backgroundImage: message['avatar'] != null
                      ? NetworkImage(message['avatar'])
                      : null,
                  child: message['avatar'] == null
                      ? Text(
                          message['name'][0].toUpperCase(),
                          style: AppTextStyles.username.copyWith(fontSize: 18),
                        )
                      : null,
                ),
                if (message['isOnline'])
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: AppColors.success,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppColors.surface,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(width: 16),
            
            // Message content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          message['name'],
                          style: AppTextStyles.username,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        message['time'],
                        style: AppTextStyles.caption,
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    message['lastMessage'],
                    style: AppTextStyles.bodySmall.copyWith(
                      color: message['unreadCount'] > 0 
                          ? AppColors.textPrimary 
                          : AppColors.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            
            // Unread count
            if (message['unreadCount'] > 0)
              Container(
                padding: const EdgeInsets.all(6),
                decoration: const BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
                constraints: const BoxConstraints(
                  minWidth: 20,
                  minHeight: 20,
                ),
                child: Text(
                  message['unreadCount'].toString(),
                  style: AppTextStyles.actionCounter.copyWith(
                    fontSize: 12,
                    color: AppColors.textOnPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: AppColors.textTertiary,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'لا توجد رسائل',
            style: AppTextStyles.h4,
          ),
          const SizedBox(height: 8),
          const Text(
            'ابدأ محادثة مع المتاجر والبائعين',
            style: AppTextStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () => _showNewMessageDialog(),
            icon: const Icon(Icons.add),
            label: const Text('رسالة جديدة'),
          ),
        ],
      ),
    );
  }

  void _openChat(Map<String, dynamic> message) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(
          chatId: message['id'],
          chatName: message['name'],
          isOnline: message['isOnline'],
        ),
      ),
    );
  }

  void _showNewMessageDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.borderRadius),
        ),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.textTertiary,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'رسالة جديدة',
              style: AppTextStyles.h4,
            ),
            const SizedBox(height: 24),
            const Text(
              'ميزة الرسائل الجديدة ستكون متاحة قريباً',
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}

class ChatScreen extends StatefulWidget {
  final String chatId;
  final String chatName;
  final bool isOnline;

  const ChatScreen({
    super.key,
    required this.chatId,
    required this.chatName,
    required this.isOnline,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final List<Map<String, dynamic>> _mockChatMessages = [
    {
      'id': '1',
      'text': 'مرحباً! كيف يمكنني مساعدتك؟',
      'isMe': false,
      'time': DateTime.now().subtract(const Duration(hours: 1)),
    },
    {
      'id': '2',
      'text': 'أريد الاستفسار عن المنتج الذي رأيته في التطبيق',
      'isMe': true,
      'time': DateTime.now().subtract(const Duration(minutes: 30)),
    },
    {
      'id': '3',
      'text': 'بالطبع! أي منتج تقصد؟ يمكنك إرسال رابط المنتج',
      'isMe': false,
      'time': DateTime.now().subtract(const Duration(minutes: 25)),
    },
  ];

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        title: Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: AppColors.surfaceVariant,
              child: Text(
                widget.chatName[0].toUpperCase(),
                style: AppTextStyles.caption,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.chatName,
                    style: AppTextStyles.username.copyWith(fontSize: 16),
                  ),
                  Text(
                    widget.isOnline ? 'متصل الآن' : 'غير متصل',
                    style: AppTextStyles.caption.copyWith(
                      color: widget.isOnline ? AppColors.success : AppColors.textTertiary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _mockChatMessages.length,
              itemBuilder: (context, index) {
                final message = _mockChatMessages[index];
                return _buildChatMessage(message);
              },
            ),
          ),
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildChatMessage(Map<String, dynamic> message) {
    final isMe = message['isMe'];
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: AppColors.surfaceVariant,
              child: Text(
                widget.chatName[0].toUpperCase(),
                style: AppTextStyles.caption,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isMe ? AppColors.primary : AppColors.surface,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                message['text'],
                style: AppTextStyles.bodyMedium.copyWith(
                  color: isMe ? AppColors.textOnPrimary : AppColors.textPrimary,
                ),
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: AppColors.primary,
              child: const Text(
                'أ',
                style: TextStyle(color: AppColors.textOnPrimary),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(color: AppColors.border, width: 0.5),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: _messageController,
                decoration: const InputDecoration(
                  hintText: 'اكتب رسالة...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                maxLines: null,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: () => _sendMessage(),
              icon: const Icon(
                Icons.send,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isNotEmpty) {
      setState(() {
        _mockChatMessages.add({
          'id': DateTime.now().millisecondsSinceEpoch.toString(),
          'text': text,
          'isMe': true,
          'time': DateTime.now(),
        });
      });
      _messageController.clear();
    }
  }
}
