import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/products_provider.dart';
import '../providers/cart_provider.dart';
import '../utils/colors.dart';
import '../utils/text_styles.dart';
import '../utils/constants.dart';
import '../widgets/shimmer_widgets.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> 
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  
  late TabController _tabController;
  int _currentTabIndex = 0;

  final List<String> _tabTitles = [
    'For You',
    'StoreTok',
    'Following',
  ];

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: _tabTitles.length,
      vsync: this,
      initialIndex: _currentTabIndex,
    );
    
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentTabIndex = _tabController.index;
        });
        HapticFeedback.lightImpact();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                physics: const BouncingScrollPhysics(),
                children: [
                  const ForYouTab(),
                  const StoreTokTab(),
                  const FollowingTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.all(4),
        labelColor: AppColors.textOnPrimary,
        unselectedLabelColor: AppColors.textSecondary,
        labelStyle: AppTextStyles.tabLabelActive.copyWith(fontSize: 14),
        unselectedLabelStyle: AppTextStyles.tabLabel.copyWith(fontSize: 14),
        dividerColor: Colors.transparent,
        overlayColor: MaterialStateProperty.all(Colors.transparent),
        splashFactory: NoSplash.splashFactory,
        tabs: _tabTitles.map((title) => Tab(
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Text(title),
          ),
        )).toList(),
      ),
    );
  }
}

class ForYouTab extends StatefulWidget {
  const ForYouTab({super.key});

  @override
  State<ForYouTab> createState() => _ForYouTabState();
}

class _ForYouTabState extends State<ForYouTab> 
    with AutomaticKeepAliveClientMixin {
  
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _loadInitialData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    final productsProvider = Provider.of<ProductsProvider>(context, listen: false);
    if (productsProvider.forYouProducts.isEmpty) {
      await productsProvider.loadForYouProducts(refresh: true);
    }
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Consumer<ProductsProvider>(
      builder: (context, productsProvider, child) {
        if (productsProvider.isLoadingForYou && productsProvider.forYouProducts.isEmpty) {
          return _buildLoadingState();
        }

        if (productsProvider.error != null && productsProvider.forYouProducts.isEmpty) {
          return _buildErrorState(productsProvider.error!);
        }

        if (productsProvider.forYouProducts.isEmpty) {
          return _buildEmptyState();
        }

        return _buildProductsList(productsProvider);
      },
    );
  }

  Widget _buildLoadingState() {
    return PageView.builder(
      scrollDirection: Axis.vertical,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 3,
      itemBuilder: (context, index) => const ProductCardShimmer(),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            error,
            style: const TextStyle(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              Provider.of<ProductsProvider>(context, listen: false)
                  .loadForYouProducts(refresh: true);
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_bag_outlined,
            size: 64,
            color: AppColors.textTertiary,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد منتجات متاحة',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList(ProductsProvider productsProvider) {
    return RefreshIndicator(
      onRefresh: () => productsProvider.loadForYouProducts(refresh: true),
      color: AppColors.primary,
      backgroundColor: AppColors.surface,
      child: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.vertical,
        onPageChanged: _onPageChanged,
        physics: const BouncingScrollPhysics(),
        itemCount: productsProvider.forYouProducts.length,
        itemBuilder: (context, index) {
          final product = productsProvider.forYouProducts[index];
          return ProductCard(
            product: product,
            isActive: index == _currentIndex,
          );
        },
      ),
    );
  }
}

// Placeholder tabs
class StoreTokTab extends StatelessWidget {
  const StoreTokTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'StoreTok Tab',
        style: TextStyle(color: AppColors.textPrimary, fontSize: 18),
      ),
    );
  }
}

class FollowingTab extends StatelessWidget {
  const FollowingTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Following Tab',
        style: TextStyle(color: AppColors.textPrimary, fontSize: 18),
      ),
    );
  }
}

// Simple product card
class ProductCard extends StatelessWidget {
  final Map<String, dynamic> product;
  final bool isActive;

  const ProductCard({
    super.key,
    required this.product,
    required this.isActive,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.background,
      child: Stack(
        children: [
          // Background image
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              image: DecorationImage(
                image: NetworkImage(product['image']),
                fit: BoxFit.cover,
                onError: (error, stackTrace) {},
              ),
            ),
          ),
          
          // Gradient overlay
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.transparent,
                  AppColors.background.withOpacity(0.3),
                  AppColors.background.withOpacity(0.8),
                ],
                stops: const [0.0, 0.5, 0.8, 1.0],
              ),
            ),
          ),
          
          // Product info
          Positioned(
            left: 16,
            right: 80,
            bottom: 100,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product['title'],
                  style: AppTextStyles.productTitle,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  '${product['price']} ر.س',
                  style: AppTextStyles.price,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Provider.of<CartProvider>(context, listen: false)
                              .addToCart(product);
                        },
                        child: const Text('أضف للسلة'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
