import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/colors.dart';
import '../../utils/text_styles.dart';
import '../../utils/constants.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> 
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Consumer<UserProvider>(
          builder: (context, userProvider, child) {
            if (!userProvider.isAuthenticated) {
              return _buildLoginPrompt();
            }

            return SingleChildScrollView(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildProfileInfo(userProvider),
                  _buildStatsRow(userProvider),
                  const SizedBox(height: 24),
                  _buildActionButtons(),
                  const SizedBox(height: 24),
                  _buildMenuItems(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoginPrompt() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.person_outline,
                size: 64,
                color: AppColors.textTertiary,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'مرحباً بك في BeshrTok',
              style: AppTextStyles.h3,
            ),
            const SizedBox(height: 8),
            const Text(
              'سجل دخولك للاستمتاع بتجربة تسوق مخصصة',
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => _showLoginDialog(),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
              child: const Text('تسجيل الدخول'),
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: () => _showRegisterDialog(),
              child: const Text('إنشاء حساب جديد'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Row(
        children: [
           Text(
            'الملف الشخصي',
            style: AppTextStyles.h3,
          ),
          const Spacer(),
          IconButton(
            onPressed: () => _showSettingsDialog(),
            icon: const Icon(
              Icons.settings_outlined,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileInfo(UserProvider userProvider) {
    final user = userProvider.currentUser!;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: Column(
        children: [
          // Avatar
          Stack(
            children: [
              CircleAvatar(
                radius: 50,
                backgroundColor: AppColors.surfaceVariant,
                backgroundImage: user.hasAvatar 
                    ? NetworkImage(user.avatar!)
                    : null,
                child: !user.hasAvatar
                    ? Text(
                        user.initials,
                        style: AppTextStyles.h3.copyWith(fontSize: 32),
                      )
                    : null,
              ),
              if (user.isVerified)
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.verified,
                      color: AppColors.textOnPrimary,
                      size: 16,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Name and username
          Text(
            user.displayName,
            style: AppTextStyles.h4,
          ),
          const SizedBox(height: 4),
          Text(
            '@${user.username}',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          
          // Bio
          if (user.hasBio) ...[
            const SizedBox(height: 12),
            Text(
              user.bio!,
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatsRow(UserProvider userProvider) {
    final user = userProvider.currentUser!;
    
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatItem('المنشورات', user.postsCount.toString()),
          _buildStatItem('المتابعون', user.formattedFollowersCount),
          _buildStatItem('المتابعة', user.formattedFollowingCount),
          _buildStatItem('الإعجابات', user.formattedLikesCount),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: AppTextStyles.h5,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTextStyles.caption,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _editProfile(),
              icon: const Icon(Icons.edit, size: 20),
              label: const Text('تعديل الملف الشخصي'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.surfaceVariant,
                foregroundColor: AppColors.textPrimary,
              ),
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () => _shareProfile(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.surfaceVariant,
              foregroundColor: AppColors.textPrimary,
              padding: const EdgeInsets.all(12),
            ),
            child: const Icon(Icons.share, size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItems() {
    final menuItems = [
      {
        'icon': Icons.favorite_outline,
        'title': 'قائمة الأمنيات',
        'subtitle': 'المنتجات المحفوظة',
        'onTap': () => _openWishlist(),
      },
      {
        'icon': Icons.history,
        'title': 'سجل الطلبات',
        'subtitle': 'طلباتك السابقة',
        'onTap': () => _openOrderHistory(),
      },
      {
        'icon': Icons.location_on_outlined,
        'title': 'العناوين',
        'subtitle': 'إدارة عناوين التوصيل',
        'onTap': () => _openAddresses(),
      },
      {
        'icon': Icons.payment_outlined,
        'title': 'طرق الدفع',
        'subtitle': 'البطاقات والمحافظ الرقمية',
        'onTap': () => _openPaymentMethods(),
      },
      {
        'icon': Icons.notifications_outlined,
        'title': 'الإشعارات',
        'subtitle': 'إعدادات التنبيهات',
        'onTap': () => _openNotificationSettings(),
      },
      {
        'icon': Icons.help_outline,
        'title': 'المساعدة والدعم',
        'subtitle': 'الأسئلة الشائعة والتواصل',
        'onTap': () => _openHelp(),
      },
      {
        'icon': Icons.info_outline,
        'title': 'حول التطبيق',
        'subtitle': 'الإصدار والشروط',
        'onTap': () => _openAbout(),
      },
      {
        'icon': Icons.logout,
        'title': 'تسجيل الخروج',
        'subtitle': 'الخروج من الحساب',
        'onTap': () => _logout(),
        'isDestructive': true,
      },
    ];

    return Column(
      children: menuItems.map((item) => _buildMenuItem(
        icon: item['icon'] as IconData,
        title: item['title'] as String,
        subtitle: item['subtitle'] as String,
        onTap: item['onTap'] as VoidCallback,
        isDestructive: item['isDestructive'] as bool? ?? false,
      )).toList(),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: 4,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? AppColors.error : AppColors.textSecondary,
        ),
        title: Text(
          title,
          style: AppTextStyles.bodyMedium.copyWith(
            color: isDestructive ? AppColors.error : AppColors.textPrimary,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: AppTextStyles.caption,
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppColors.textTertiary,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        tileColor: AppColors.surface,
      ),
    );
  }

  void _showLoginDialog() {
    // Show login dialog
    debugPrint('Show login dialog');
  }

  void _showRegisterDialog() {
    // Show register dialog
    debugPrint('Show register dialog');
  }

  void _showSettingsDialog() {
    // Show settings dialog
    debugPrint('Show settings dialog');
  }

  void _editProfile() {
    // Navigate to edit profile screen
    debugPrint('Edit profile');
  }

  void _shareProfile() {
    // Share profile functionality
    debugPrint('Share profile');
  }

  void _openWishlist() {
    // Navigate to wishlist screen
    debugPrint('Open wishlist');
  }

  void _openOrderHistory() {
    // Navigate to order history screen
    debugPrint('Open order history');
  }

  void _openAddresses() {
    // Navigate to addresses screen
    debugPrint('Open addresses');
  }

  void _openPaymentMethods() {
    // Navigate to payment methods screen
    debugPrint('Open payment methods');
  }

  void _openNotificationSettings() {
    // Navigate to notification settings screen
    debugPrint('Open notification settings');
  }

  void _openHelp() {
    // Navigate to help screen
    debugPrint('Open help');
  }

  void _openAbout() {
    // Navigate to about screen
    debugPrint('Open about');
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surface,
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<UserProvider>(context, listen: false).logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
