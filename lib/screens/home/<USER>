import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/products_provider.dart';
import '../../widgets/product_card.dart';
import '../../utils/colors.dart';
import '../../utils/text_styles.dart';
import '../../utils/constants.dart';

class FollowingTab extends StatefulWidget {
  const FollowingTab({super.key});

  @override
  State<FollowingTab> createState() => _FollowingTabState();
}

class _FollowingTabState extends State<FollowingTab> 
    with AutomaticKeepAliveClientMixin {
  
  late PageController _pageController;
  int _currentIndex = 0;
  bool _isLoading = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _loadInitialData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    final productsProvider = Provider.of<ProductsProvider>(context, listen: false);
    if (productsProvider.followingProducts.isEmpty) {
      await productsProvider.loadFollowingProducts(refresh: true);
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    final productsProvider = Provider.of<ProductsProvider>(context, listen: false);
    await productsProvider.loadFollowingProducts();

    setState(() {
      _isLoading = false;
    });
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    final productsProvider = Provider.of<ProductsProvider>(context, listen: false);
    
    // Load more data when approaching the end
    if (index >= productsProvider.followingProducts.length - 3 && 
        productsProvider.hasMoreFollowing && 
        !productsProvider.isLoadingFollowing) {
      _loadMoreData();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Consumer<ProductsProvider>(
      builder: (context, productsProvider, child) {
        if (productsProvider.isLoadingFollowing && productsProvider.followingProducts.isEmpty) {
          return _buildLoadingState();
        }

        if (productsProvider.error != null && productsProvider.followingProducts.isEmpty) {
          return _buildErrorState(productsProvider.error!);
        }

        if (productsProvider.followingProducts.isEmpty) {
          return _buildEmptyState();
        }

        return _buildProductsList(productsProvider);
      },
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل منتجات المتابعين...',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.people_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            error,
            style: const TextStyle(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              Provider.of<ProductsProvider>(context, listen: false)
                  .loadFollowingProducts(refresh: true);
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.people_outline,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد منتجات من المتابعين',
            style: AppTextStyles.h5,
          ),
          const SizedBox(height: 8),
          const Text(
            'تابع المتاجر والبائعين المفضلين لديك\nلرؤية منتجاتهم الجديدة هنا',
            style: AppTextStyles.caption,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to discover page
            },
            icon: const Icon(Icons.explore),
            label: const Text('اكتشف المتاجر'),
          ),
          const SizedBox(height: 12),
          TextButton.icon(
            onPressed: () {
              // Navigate to suggested users/stores
            },
            icon: const Icon(Icons.person_add),
            label: const Text('اقتراحات للمتابعة'),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList(ProductsProvider productsProvider) {
    return RefreshIndicator(
      onRefresh: () => productsProvider.loadFollowingProducts(refresh: true),
      color: AppColors.primary,
      backgroundColor: AppColors.surface,
      child: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.vertical,
        onPageChanged: _onPageChanged,
        physics: const BouncingScrollPhysics(),
        itemCount: productsProvider.followingProducts.length + 
                   (productsProvider.hasMoreFollowing ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= productsProvider.followingProducts.length) {
            // Loading indicator for more items
            return _buildLoadingMoreIndicator();
          }

          final product = productsProvider.followingProducts[index];
          return TikTokStyleProductCard(
            product: product,
            isActive: index == _currentIndex,
          );
        },
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل المزيد من المنتجات...',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }
}
