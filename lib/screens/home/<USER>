import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/products_provider.dart';
import '../../widgets/product_card.dart';
import '../../widgets/shimmer_widgets.dart';
import '../../utils/colors.dart';
import '../../utils/constants.dart';

class ForYouTab extends StatefulWidget {
  const ForYouTab({super.key});

  @override
  State<ForYouTab> createState() => _ForYouTabState();
}

class _ForYouTabState extends State<ForYouTab> 
    with AutomaticKeepAliveClientMixin {
  
  late PageController _pageController;
  int _currentIndex = 0;
  bool _isLoading = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _loadInitialData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    final productsProvider = Provider.of<ProductsProvider>(context, listen: false);
    if (productsProvider.forYouProducts.isEmpty) {
      await productsProvider.loadForYouProducts(refresh: true);
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    final productsProvider = Provider.of<ProductsProvider>(context, listen: false);
    await productsProvider.loadForYouProducts();

    setState(() {
      _isLoading = false;
    });
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    final productsProvider = Provider.of<ProductsProvider>(context, listen: false);
    
    // Load more data when approaching the end
    if (index >= productsProvider.forYouProducts.length - 3 && 
        productsProvider.hasMoreForYou && 
        !productsProvider.isLoadingForYou) {
      _loadMoreData();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Consumer<ProductsProvider>(
      builder: (context, productsProvider, child) {
        if (productsProvider.isLoadingForYou && productsProvider.forYouProducts.isEmpty) {
          return _buildLoadingState();
        }

        if (productsProvider.error != null && productsProvider.forYouProducts.isEmpty) {
          return _buildErrorState(productsProvider.error!);
        }

        if (productsProvider.forYouProducts.isEmpty) {
          return _buildEmptyState();
        }

        return _buildProductsList(productsProvider);
      },
    );
  }

  Widget _buildLoadingState() {
    return PageView.builder(
      scrollDirection: Axis.vertical,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 3,
      itemBuilder: (context, index) => const ProductCardShimmer(),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            error,
            style: const TextStyle(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              Provider.of<ProductsProvider>(context, listen: false)
                  .loadForYouProducts(refresh: true);
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.shopping_bag_outlined,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد منتجات متاحة',
            style: TextStyle(color: AppColors.textSecondary),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              Provider.of<ProductsProvider>(context, listen: false)
                  .loadForYouProducts(refresh: true);
            },
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList(ProductsProvider productsProvider) {
    return RefreshIndicator(
      onRefresh: () => productsProvider.loadForYouProducts(refresh: true),
      color: AppColors.primary,
      backgroundColor: AppColors.surface,
      child: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.vertical,
        onPageChanged: _onPageChanged,
        physics: const BouncingScrollPhysics(),
        itemCount: productsProvider.forYouProducts.length + 
                   (productsProvider.hasMoreForYou ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= productsProvider.forYouProducts.length) {
            // Loading indicator for more items
            return _buildLoadingMoreIndicator();
          }

          final product = productsProvider.forYouProducts[index];
          return TikTokStyleProductCard(
            product: product,
            isActive: index == _currentIndex,
          );
        },
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل المزيد...',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }
}

// Custom scroll physics for TikTok-like experience
class TikTokScrollPhysics extends ScrollPhysics {
  const TikTokScrollPhysics({super.parent});

  @override
  TikTokScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return TikTokScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  SpringDescription get spring => const SpringDescription(
    mass: 80,
    stiffness: 100,
    damping: 1,
  );

  @override
  double get minFlingVelocity => 200.0;

  @override
  double get maxFlingVelocity => 8000.0;
}

// Preload widget for better performance
class PreloadPageView extends StatefulWidget {
  final PageController controller;
  final ValueChanged<int>? onPageChanged;
  final IndexedWidgetBuilder itemBuilder;
  final int itemCount;
  final int preloadPagesCount;

  const PreloadPageView({
    super.key,
    required this.controller,
    this.onPageChanged,
    required this.itemBuilder,
    required this.itemCount,
    this.preloadPagesCount = 3,
  });

  @override
  State<PreloadPageView> createState() => _PreloadPageViewState();
}

class _PreloadPageViewState extends State<PreloadPageView> {
  late int _currentIndex;
  final Map<int, Widget> _preloadedPages = {};

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.controller.initialPage;
    _preloadPages();
  }

  void _preloadPages() {
    final start = (_currentIndex - widget.preloadPagesCount).clamp(0, widget.itemCount - 1);
    final end = (_currentIndex + widget.preloadPagesCount).clamp(0, widget.itemCount - 1);

    for (int i = start; i <= end; i++) {
      if (!_preloadedPages.containsKey(i)) {
        _preloadedPages[i] = widget.itemBuilder(context, i);
      }
    }

    // Remove pages that are too far away
    _preloadedPages.removeWhere((index, _) => 
        index < start - widget.preloadPagesCount || 
        index > end + widget.preloadPagesCount);
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: widget.controller,
      scrollDirection: Axis.vertical,
      physics: const TikTokScrollPhysics(),
      onPageChanged: (index) {
        setState(() {
          _currentIndex = index;
        });
        _preloadPages();
        widget.onPageChanged?.call(index);
      },
      itemCount: widget.itemCount,
      itemBuilder: (context, index) {
        return _preloadedPages[index] ?? widget.itemBuilder(context, index);
      },
    );
  }
}
