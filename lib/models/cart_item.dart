import 'product.dart';

class CartItem {
  final String id;
  final Product product;
  final int quantity;
  final String? selectedVariant;
  final Map<String, dynamic>? selectedOptions;
  final DateTime addedAt;
  final double unitPrice;
  final double totalPrice;

  CartItem({
    required this.id,
    required this.product,
    required this.quantity,
    this.selectedVariant,
    this.selectedOptions,
    required this.addedAt,
    required this.unitPrice,
    required this.totalPrice,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      id: json['id'] ?? '',
      product: Product.fromJson(json['product'] ?? {}),
      quantity: json['quantity'] ?? 1,
      selectedVariant: json['selected_variant'],
      selectedOptions: json['selected_options'],
      addedAt: DateTime.parse(json['added_at'] ?? DateTime.now().toIso8601String()),
      unitPrice: (json['unit_price'] ?? 0).toDouble(),
      totalPrice: (json['total_price'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product': product.toJson(),
      'quantity': quantity,
      'selected_variant': selectedVariant,
      'selected_options': selectedOptions,
      'added_at': addedAt.toIso8601String(),
      'unit_price': unitPrice,
      'total_price': totalPrice,
    };
  }

  CartItem copyWith({
    String? id,
    Product? product,
    int? quantity,
    String? selectedVariant,
    Map<String, dynamic>? selectedOptions,
    DateTime? addedAt,
    double? unitPrice,
    double? totalPrice,
  }) {
    return CartItem(
      id: id ?? this.id,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      selectedVariant: selectedVariant ?? this.selectedVariant,
      selectedOptions: selectedOptions ?? this.selectedOptions,
      addedAt: addedAt ?? this.addedAt,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }

  String get formattedUnitPrice {
    return '$unitPrice ${product.currency}';
  }

  String get formattedTotalPrice {
    return '$totalPrice ${product.currency}';
  }

  bool get hasVariant {
    return selectedVariant != null && selectedVariant!.isNotEmpty;
  }

  bool get hasOptions {
    return selectedOptions != null && selectedOptions!.isNotEmpty;
  }

  double get calculatedTotalPrice {
    return unitPrice * quantity;
  }

  bool get isValidQuantity {
    return quantity > 0 && quantity <= product.stockQuantity;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem &&
        other.id == id &&
        other.product.id == product.id &&
        other.selectedVariant == selectedVariant;
  }

  @override
  int get hashCode {
    return id.hashCode ^ product.id.hashCode ^ selectedVariant.hashCode;
  }
}

class Cart {
  final List<CartItem> items;
  final double subtotal;
  final double tax;
  final double shipping;
  final double discount;
  final double total;
  final String? couponCode;
  final DateTime updatedAt;

  Cart({
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.shipping,
    required this.discount,
    required this.total,
    this.couponCode,
    required this.updatedAt,
  });

  factory Cart.empty() {
    return Cart(
      items: [],
      subtotal: 0,
      tax: 0,
      shipping: 0,
      discount: 0,
      total: 0,
      updatedAt: DateTime.now(),
    );
  }

  factory Cart.fromJson(Map<String, dynamic> json) {
    return Cart(
      items: (json['items'] as List? ?? [])
          .map((item) => CartItem.fromJson(item))
          .toList(),
      subtotal: (json['subtotal'] ?? 0).toDouble(),
      tax: (json['tax'] ?? 0).toDouble(),
      shipping: (json['shipping'] ?? 0).toDouble(),
      discount: (json['discount'] ?? 0).toDouble(),
      total: (json['total'] ?? 0).toDouble(),
      couponCode: json['coupon_code'],
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'shipping': shipping,
      'discount': discount,
      'total': total,
      'coupon_code': couponCode,
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Cart copyWith({
    List<CartItem>? items,
    double? subtotal,
    double? tax,
    double? shipping,
    double? discount,
    double? total,
    String? couponCode,
    DateTime? updatedAt,
  }) {
    return Cart(
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shipping: shipping ?? this.shipping,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      couponCode: couponCode ?? this.couponCode,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
  int get itemCount => items.length;
  int get totalQuantity => items.fold(0, (sum, item) => sum + item.quantity);

  String get formattedSubtotal => '$subtotal ر.س';
  String get formattedTax => '$tax ر.س';
  String get formattedShipping => '$shipping ر.س';
  String get formattedDiscount => '$discount ر.س';
  String get formattedTotal => '$total ر.س';

  bool get hasCoupon => couponCode != null && couponCode!.isNotEmpty;
  bool get hasDiscount => discount > 0;
  bool get hasShipping => shipping > 0;

  double get calculatedSubtotal {
    return items.fold(0, (sum, item) => sum + item.totalPrice);
  }
}
