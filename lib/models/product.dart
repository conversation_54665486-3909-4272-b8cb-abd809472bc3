class Product {
  final String id;
  final String title;
  final String description;
  final double price;
  final double? originalPrice;
  final String currency;
  final List<String> images;
  final String? videoUrl;
  final String category;
  final String brand;
  final double rating;
  final int reviewCount;
  final int stockQuantity;
  final bool isAvailable;
  final bool isFeatured;
  final bool isOnSale;
  final double? discountPercentage;
  final List<String> tags;
  final Map<String, dynamic> attributes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String sellerId;
  final String sellerName;
  final String? sellerAvatar;
  final int likeCount;
  final int commentCount;
  final int shareCount;
  final bool isLiked;
  final bool isBookmarked;

  Product({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    this.originalPrice,
    required this.currency,
    required this.images,
    this.videoUrl,
    required this.category,
    required this.brand,
    required this.rating,
    required this.reviewCount,
    required this.stockQuantity,
    required this.isAvailable,
    required this.isFeatured,
    required this.isOnSale,
    this.discountPercentage,
    required this.tags,
    required this.attributes,
    required this.createdAt,
    required this.updatedAt,
    required this.sellerId,
    required this.sellerName,
    this.sellerAvatar,
    required this.likeCount,
    required this.commentCount,
    required this.shareCount,
    required this.isLiked,
    required this.isBookmarked,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      originalPrice: json['original_price']?.toDouble(),
      currency: json['currency'] ?? 'SAR',
      images: List<String>.from(json['images'] ?? []),
      videoUrl: json['video_url'],
      category: json['category'] ?? '',
      brand: json['brand'] ?? '',
      rating: (json['rating'] ?? 0).toDouble(),
      reviewCount: json['review_count'] ?? 0,
      stockQuantity: json['stock_quantity'] ?? 0,
      isAvailable: json['is_available'] ?? false,
      isFeatured: json['is_featured'] ?? false,
      isOnSale: json['is_on_sale'] ?? false,
      discountPercentage: json['discount_percentage']?.toDouble(),
      tags: List<String>.from(json['tags'] ?? []),
      attributes: json['attributes'] ?? {},
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      sellerId: json['seller_id'] ?? '',
      sellerName: json['seller_name'] ?? '',
      sellerAvatar: json['seller_avatar'],
      likeCount: json['like_count'] ?? 0,
      commentCount: json['comment_count'] ?? 0,
      shareCount: json['share_count'] ?? 0,
      isLiked: json['is_liked'] ?? false,
      isBookmarked: json['is_bookmarked'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'price': price,
      'original_price': originalPrice,
      'currency': currency,
      'images': images,
      'video_url': videoUrl,
      'category': category,
      'brand': brand,
      'rating': rating,
      'review_count': reviewCount,
      'stock_quantity': stockQuantity,
      'is_available': isAvailable,
      'is_featured': isFeatured,
      'is_on_sale': isOnSale,
      'discount_percentage': discountPercentage,
      'tags': tags,
      'attributes': attributes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'seller_id': sellerId,
      'seller_name': sellerName,
      'seller_avatar': sellerAvatar,
      'like_count': likeCount,
      'comment_count': commentCount,
      'share_count': shareCount,
      'is_liked': isLiked,
      'is_bookmarked': isBookmarked,
    };
  }

  Product copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    double? originalPrice,
    String? currency,
    List<String>? images,
    String? videoUrl,
    String? category,
    String? brand,
    double? rating,
    int? reviewCount,
    int? stockQuantity,
    bool? isAvailable,
    bool? isFeatured,
    bool? isOnSale,
    double? discountPercentage,
    List<String>? tags,
    Map<String, dynamic>? attributes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? sellerId,
    String? sellerName,
    String? sellerAvatar,
    int? likeCount,
    int? commentCount,
    int? shareCount,
    bool? isLiked,
    bool? isBookmarked,
  }) {
    return Product(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      currency: currency ?? this.currency,
      images: images ?? this.images,
      videoUrl: videoUrl ?? this.videoUrl,
      category: category ?? this.category,
      brand: brand ?? this.brand,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      isAvailable: isAvailable ?? this.isAvailable,
      isFeatured: isFeatured ?? this.isFeatured,
      isOnSale: isOnSale ?? this.isOnSale,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      tags: tags ?? this.tags,
      attributes: attributes ?? this.attributes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      sellerId: sellerId ?? this.sellerId,
      sellerName: sellerName ?? this.sellerName,
      sellerAvatar: sellerAvatar ?? this.sellerAvatar,
      likeCount: likeCount ?? this.likeCount,
      commentCount: commentCount ?? this.commentCount,
      shareCount: shareCount ?? this.shareCount,
      isLiked: isLiked ?? this.isLiked,
      isBookmarked: isBookmarked ?? this.isBookmarked,
    );
  }

  String get formattedPrice {
    return '$price $currency';
  }

  String? get formattedOriginalPrice {
    if (originalPrice == null) return null;
    return '$originalPrice $currency';
  }

  String get formattedRating {
    return rating.toStringAsFixed(1);
  }

  bool get hasDiscount {
    return originalPrice != null && originalPrice! > price;
  }

  double get calculatedDiscountPercentage {
    if (!hasDiscount) return 0;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }

  String get formattedDiscountPercentage {
    return '${calculatedDiscountPercentage.round()}%';
  }

  bool get isInStock {
    return isAvailable && stockQuantity > 0;
  }

  String get primaryImage {
    return images.isNotEmpty ? images.first : '';
  }

  bool get hasVideo {
    return videoUrl != null && videoUrl!.isNotEmpty;
  }
}
