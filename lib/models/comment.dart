class Comment {
  final String id;
  final String productId;
  final String userId;
  final String username;
  final String? userAvatar;
  final String text;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int likeCount;
  final bool isLiked;
  final bool isVerified;
  final String? parentCommentId; // For replies
  final List<Comment> replies;
  final bool isPinned;
  final bool isEdited;

  Comment({
    required this.id,
    required this.productId,
    required this.userId,
    required this.username,
    this.userAvatar,
    required this.text,
    required this.createdAt,
    required this.updatedAt,
    required this.likeCount,
    required this.isLiked,
    required this.isVerified,
    this.parentCommentId,
    required this.replies,
    required this.isPinned,
    required this.isEdited,
  });

  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      id: json['id'] ?? '',
      productId: json['product_id'] ?? '',
      userId: json['user_id'] ?? '',
      username: json['username'] ?? '',
      userAvatar: json['user_avatar'],
      text: json['text'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      likeCount: json['like_count'] ?? 0,
      isLiked: json['is_liked'] ?? false,
      isVerified: json['is_verified'] ?? false,
      parentCommentId: json['parent_comment_id'],
      replies: (json['replies'] as List? ?? [])
          .map((reply) => Comment.fromJson(reply))
          .toList(),
      isPinned: json['is_pinned'] ?? false,
      isEdited: json['is_edited'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'user_id': userId,
      'username': username,
      'user_avatar': userAvatar,
      'text': text,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'like_count': likeCount,
      'is_liked': isLiked,
      'is_verified': isVerified,
      'parent_comment_id': parentCommentId,
      'replies': replies.map((reply) => reply.toJson()).toList(),
      'is_pinned': isPinned,
      'is_edited': isEdited,
    };
  }

  Comment copyWith({
    String? id,
    String? productId,
    String? userId,
    String? username,
    String? userAvatar,
    String? text,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? likeCount,
    bool? isLiked,
    bool? isVerified,
    String? parentCommentId,
    List<Comment>? replies,
    bool? isPinned,
    bool? isEdited,
  }) {
    return Comment(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      userAvatar: userAvatar ?? this.userAvatar,
      text: text ?? this.text,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likeCount: likeCount ?? this.likeCount,
      isLiked: isLiked ?? this.isLiked,
      isVerified: isVerified ?? this.isVerified,
      parentCommentId: parentCommentId ?? this.parentCommentId,
      replies: replies ?? this.replies,
      isPinned: isPinned ?? this.isPinned,
      isEdited: isEdited ?? this.isEdited,
    );
  }

  bool get isReply => parentCommentId != null;
  bool get hasReplies => replies.isNotEmpty;
  int get totalReplies => replies.length;

  String get formattedLikeCount {
    if (likeCount >= 1000000) {
      return '${(likeCount / 1000000).toStringAsFixed(1)}M';
    } else if (likeCount >= 1000) {
      return '${(likeCount / 1000).toStringAsFixed(1)}K';
    } else {
      return likeCount.toString();
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return 'منذ ${years} ${years == 1 ? 'سنة' : 'سنوات'}';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return 'منذ ${months} ${months == 1 ? 'شهر' : 'أشهر'}';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  String get userInitials {
    if (username.isEmpty) return 'U';
    final words = username.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else {
      return username[0].toUpperCase();
    }
  }

  bool get hasAvatar => userAvatar != null && userAvatar!.isNotEmpty;

  // Add a reply to this comment
  Comment addReply(Comment reply) {
    return copyWith(
      replies: [...replies, reply.copyWith(parentCommentId: id)],
    );
  }

  // Remove a reply from this comment
  Comment removeReply(String replyId) {
    return copyWith(
      replies: replies.where((reply) => reply.id != replyId).toList(),
    );
  }

  // Update a reply in this comment
  Comment updateReply(Comment updatedReply) {
    return copyWith(
      replies: replies.map((reply) {
        if (reply.id == updatedReply.id) {
          return updatedReply;
        }
        return reply;
      }).toList(),
    );
  }

  // Toggle like status
  Comment toggleLike() {
    return copyWith(
      isLiked: !isLiked,
      likeCount: isLiked ? likeCount - 1 : likeCount + 1,
    );
  }

  // Pin/unpin comment
  Comment togglePin() {
    return copyWith(isPinned: !isPinned);
  }

  // Mark as edited
  Comment markAsEdited(String newText) {
    return copyWith(
      text: newText,
      isEdited: true,
      updatedAt: DateTime.now(),
    );
  }
}

// Mock data generator for comments
class CommentMockData {
  static List<Comment> generateMockComments(String productId, {int count = 10}) {
    final List<Comment> comments = [];
    final now = DateTime.now();

    for (int i = 0; i < count; i++) {
      final comment = Comment(
        id: 'comment_$i',
        productId: productId,
        userId: 'user_$i',
        username: _getMockUsername(i),
        userAvatar: i % 3 == 0 ? 'https://picsum.photos/100/100?random=$i' : null,
        text: _getMockCommentText(i),
        createdAt: now.subtract(Duration(hours: i * 2, minutes: i * 15)),
        updatedAt: now.subtract(Duration(hours: i * 2, minutes: i * 15)),
        likeCount: (i * 3) + (i % 7),
        isLiked: i % 5 == 0,
        isVerified: i % 4 == 0,
        parentCommentId: null,
        replies: _generateMockReplies('comment_$i', i),
        isPinned: i == 0, // Pin the first comment
        isEdited: i % 6 == 0,
      );
      comments.add(comment);
    }

    return comments;
  }

  static List<Comment> _generateMockReplies(String parentId, int parentIndex) {
    if (parentIndex % 3 != 0) return []; // Not all comments have replies

    final List<Comment> replies = [];
    final replyCount = (parentIndex % 3) + 1;
    final now = DateTime.now();

    for (int i = 0; i < replyCount; i++) {
      final reply = Comment(
        id: '${parentId}_reply_$i',
        productId: 'product_1',
        userId: 'user_reply_$i',
        username: _getMockUsername(i + 100),
        userAvatar: i % 2 == 0 ? 'https://picsum.photos/100/100?random=${i + 100}' : null,
        text: _getMockReplyText(i),
        createdAt: now.subtract(Duration(hours: parentIndex * 2 - i, minutes: i * 10)),
        updatedAt: now.subtract(Duration(hours: parentIndex * 2 - i, minutes: i * 10)),
        likeCount: i * 2,
        isLiked: i % 3 == 0,
        isVerified: i % 5 == 0,
        parentCommentId: parentId,
        replies: [],
        isPinned: false,
        isEdited: false,
      );
      replies.add(reply);
    }

    return replies;
  }

  static String _getMockUsername(int index) {
    final names = [
      'أحمد محمد',
      'فاطمة علي',
      'محمد أحمد',
      'نور الهدى',
      'عبدالله سالم',
      'مريم خالد',
      'يوسف عمر',
      'زينب حسن',
      'خالد محمود',
      'سارة أحمد',
    ];
    return names[index % names.length];
  }

  static String _getMockCommentText(int index) {
    final comments = [
      'منتج رائع جداً! أنصح الجميع بشرائه 👍',
      'جودة ممتازة والسعر مناسب',
      'وصل المنتج بسرعة والتغليف محترم',
      'استخدمته لفترة والنتيجة مذهلة',
      'يستحق الشراء بجدارة، جودة عالية',
      'خدمة العملاء ممتازة والمنتج كما هو موصوف',
      'سعر مناسب مقارنة بالجودة المقدمة',
      'أفضل منتج اشتريته من هذا المتجر',
      'التوصيل سريع والمنتج أصلي 100%',
      'تجربة شراء ممتازة، سأكرر الطلب',
    ];
    return comments[index % comments.length];
  }

  static String _getMockReplyText(int index) {
    final replies = [
      'أتفق معك تماماً',
      'شكراً لك على المراجعة المفيدة',
      'هل يمكنك مشاركة المزيد من التفاصيل؟',
      'جربت نفس المنتج والنتيجة رائعة',
      'أين يمكنني الحصول على نفس العرض؟',
      'متى تتوقع وصول المنتج؟',
      'هل هناك ألوان أخرى متاحة؟',
      'ما هي مدة الضمان؟',
    ];
    return replies[index % replies.length];
  }
}
