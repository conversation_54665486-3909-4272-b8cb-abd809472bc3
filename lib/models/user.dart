class User {
  final String id;
  final String username;
  final String email;
  final String? phone;
  final String? firstName;
  final String? lastName;
  final String? avatar;
  final String? bio;
  final DateTime? birthDate;
  final String? gender;
  final String? country;
  final String? city;
  final bool isVerified;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int followersCount;
  final int followingCount;
  final int postsCount;
  final int likesCount;
  final bool isFollowing;
  final bool isFollowedBy;
  final UserPreferences preferences;

  User({
    required this.id,
    required this.username,
    required this.email,
    this.phone,
    this.firstName,
    this.lastName,
    this.avatar,
    this.bio,
    this.birthDate,
    this.gender,
    this.country,
    this.city,
    required this.isVerified,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.followersCount,
    required this.followingCount,
    required this.postsCount,
    required this.likesCount,
    required this.isFollowing,
    required this.isFollowedBy,
    required this.preferences,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      avatar: json['avatar'],
      bio: json['bio'],
      birthDate: json['birth_date'] != null ? DateTime.parse(json['birth_date']) : null,
      gender: json['gender'],
      country: json['country'],
      city: json['city'],
      isVerified: json['is_verified'] ?? false,
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      followersCount: json['followers_count'] ?? 0,
      followingCount: json['following_count'] ?? 0,
      postsCount: json['posts_count'] ?? 0,
      likesCount: json['likes_count'] ?? 0,
      isFollowing: json['is_following'] ?? false,
      isFollowedBy: json['is_followed_by'] ?? false,
      preferences: UserPreferences.fromJson(json['preferences'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'phone': phone,
      'first_name': firstName,
      'last_name': lastName,
      'avatar': avatar,
      'bio': bio,
      'birth_date': birthDate?.toIso8601String(),
      'gender': gender,
      'country': country,
      'city': city,
      'is_verified': isVerified,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'followers_count': followersCount,
      'following_count': followingCount,
      'posts_count': postsCount,
      'likes_count': likesCount,
      'is_following': isFollowing,
      'is_followed_by': isFollowedBy,
      'preferences': preferences.toJson(),
    };
  }

  User copyWith({
    String? id,
    String? username,
    String? email,
    String? phone,
    String? firstName,
    String? lastName,
    String? avatar,
    String? bio,
    DateTime? birthDate,
    String? gender,
    String? country,
    String? city,
    bool? isVerified,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? followersCount,
    int? followingCount,
    int? postsCount,
    int? likesCount,
    bool? isFollowing,
    bool? isFollowedBy,
    UserPreferences? preferences,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      avatar: avatar ?? this.avatar,
      bio: bio ?? this.bio,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      country: country ?? this.country,
      city: city ?? this.city,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      postsCount: postsCount ?? this.postsCount,
      likesCount: likesCount ?? this.likesCount,
      isFollowing: isFollowing ?? this.isFollowing,
      isFollowedBy: isFollowedBy ?? this.isFollowedBy,
      preferences: preferences ?? this.preferences,
    );
  }

  String get displayName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else {
      return username;
    }
  }

  String get initials {
    if (firstName != null && lastName != null) {
      return '${firstName![0]}${lastName![0]}'.toUpperCase();
    } else if (firstName != null) {
      return firstName![0].toUpperCase();
    } else {
      return username[0].toUpperCase();
    }
  }

  bool get hasAvatar => avatar != null && avatar!.isNotEmpty;
  bool get hasBio => bio != null && bio!.isNotEmpty;
  bool get hasPhone => phone != null && phone!.isNotEmpty;
  bool get hasLocation => country != null || city != null;

  String get formattedFollowersCount {
    if (followersCount >= 1000000) {
      return '${(followersCount / 1000000).toStringAsFixed(1)}M';
    } else if (followersCount >= 1000) {
      return '${(followersCount / 1000).toStringAsFixed(1)}K';
    } else {
      return followersCount.toString();
    }
  }

  String get formattedFollowingCount {
    if (followingCount >= 1000000) {
      return '${(followingCount / 1000000).toStringAsFixed(1)}M';
    } else if (followingCount >= 1000) {
      return '${(followingCount / 1000).toStringAsFixed(1)}K';
    } else {
      return followingCount.toString();
    }
  }

  String get formattedLikesCount {
    if (likesCount >= 1000000) {
      return '${(likesCount / 1000000).toStringAsFixed(1)}M';
    } else if (likesCount >= 1000) {
      return '${(likesCount / 1000).toStringAsFixed(1)}K';
    } else {
      return likesCount.toString();
    }
  }
}

class UserPreferences {
  final bool notificationsEnabled;
  final bool emailNotifications;
  final bool pushNotifications;
  final bool smsNotifications;
  final String language;
  final String theme;
  final bool autoPlayVideos;
  final bool showOnlineStatus;
  final bool allowDirectMessages;
  final bool showInSearch;
  final Map<String, dynamic> privacySettings;

  UserPreferences({
    required this.notificationsEnabled,
    required this.emailNotifications,
    required this.pushNotifications,
    required this.smsNotifications,
    required this.language,
    required this.theme,
    required this.autoPlayVideos,
    required this.showOnlineStatus,
    required this.allowDirectMessages,
    required this.showInSearch,
    required this.privacySettings,
  });

  factory UserPreferences.defaultPreferences() {
    return UserPreferences(
      notificationsEnabled: true,
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      language: 'ar',
      theme: 'dark',
      autoPlayVideos: true,
      showOnlineStatus: true,
      allowDirectMessages: true,
      showInSearch: true,
      privacySettings: {},
    );
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      notificationsEnabled: json['notifications_enabled'] ?? true,
      emailNotifications: json['email_notifications'] ?? true,
      pushNotifications: json['push_notifications'] ?? true,
      smsNotifications: json['sms_notifications'] ?? false,
      language: json['language'] ?? 'ar',
      theme: json['theme'] ?? 'dark',
      autoPlayVideos: json['auto_play_videos'] ?? true,
      showOnlineStatus: json['show_online_status'] ?? true,
      allowDirectMessages: json['allow_direct_messages'] ?? true,
      showInSearch: json['show_in_search'] ?? true,
      privacySettings: json['privacy_settings'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notifications_enabled': notificationsEnabled,
      'email_notifications': emailNotifications,
      'push_notifications': pushNotifications,
      'sms_notifications': smsNotifications,
      'language': language,
      'theme': theme,
      'auto_play_videos': autoPlayVideos,
      'show_online_status': showOnlineStatus,
      'allow_direct_messages': allowDirectMessages,
      'show_in_search': showInSearch,
      'privacy_settings': privacySettings,
    };
  }

  UserPreferences copyWith({
    bool? notificationsEnabled,
    bool? emailNotifications,
    bool? pushNotifications,
    bool? smsNotifications,
    String? language,
    String? theme,
    bool? autoPlayVideos,
    bool? showOnlineStatus,
    bool? allowDirectMessages,
    bool? showInSearch,
    Map<String, dynamic>? privacySettings,
  }) {
    return UserPreferences(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      language: language ?? this.language,
      theme: theme ?? this.theme,
      autoPlayVideos: autoPlayVideos ?? this.autoPlayVideos,
      showOnlineStatus: showOnlineStatus ?? this.showOnlineStatus,
      allowDirectMessages: allowDirectMessages ?? this.allowDirectMessages,
      showInSearch: showInSearch ?? this.showInSearch,
      privacySettings: privacySettings ?? this.privacySettings,
    );
  }
}
