/// Flutter icons TikTokIcons
/// Copyright (C) 2019 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  TikTokIcons
///      fonts:
///       - asset: fonts/TikTokIcons.ttf
///
/// 
///
import 'package:flutter/widgets.dart';

class TikTokIcons {
  TikTokIcons._();

  static const _kFontFam = 'TikTokIcons';

  static const IconData chat_bubble = const IconData(0xe808, fontFamily: _kFontFam);
  static const IconData create = const IconData(0xe809, fontFamily: _kFontFam);
  static const IconData heart = const IconData(0xe80a, fontFamily: _kFontFam);
  static const IconData home = const IconData(0xe80b, fontFamily: _kFontFam);
  static const IconData messages = const IconData(0xe80c, fontFamily: _kFontFam);
  static const IconData profile = const IconData(0xe80d, fontFamily: _kFontFam);
  static const IconData reply = const IconData(0xe80e, fontFamily: _kFontFam);
  static const IconData search = const IconData(0xe80f, fontFamily: _kFontFam);
}