import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - TikTok inspired
  static const Color primary = Color(0xFFFF0050);
  static const Color primaryDark = Color(0xFFE6004A);
  static const Color secondary = Color(0xFF25F4EE);
  static const Color accent = Color(0xFFFE2C55);
  
  // Background Colors
  static const Color background = Color(0xFF000000);
  static const Color surface = Color(0xFF161823);
  static const Color surfaceVariant = Color(0xFF1F1F23);
  static const Color cardBackground = Color(0xFF2F2F2F);
  
  // Text Colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFBBBBBB);
  static const Color textTertiary = Color(0xFF888888);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // Action Colors
  static const Color like = Color(0xFFFF3040);
  static const Color comment = Color(0xFFFFFFFF);
  static const Color share = Color(0xFFFFFFFF);
  static const Color bookmark = Color(0xFFFFD700);
  
  // E-commerce Colors
  static const Color price = Color(0xFF00D4AA);
  static const Color discount = Color(0xFFFF6B35);
  static const Color addToCart = Color(0xFF4CAF50);
  static const Color buyNow = Color(0xFFFF9800);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFFF5252);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [secondary, accent],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [background, surface],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // Overlay Colors
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x40000000);
  static const Color overlayDark = Color(0xB3000000);
  
  // Border Colors
  static const Color border = Color(0xFF333333);
  static const Color borderLight = Color(0xFF555555);
  static const Color borderDark = Color(0xFF222222);
}
