import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFFFF006E);
  static const Color secondary = Color(0xFF8338EC);
  static const Color accent = Color(0xFF3A86FF);
  
  // Background Colors
  static const Color background = Color(0xFF000000);
  static const Color surface = Color(0xFF1A1A1A);
  static const Color surfaceVariant = Color(0xFF2A2A2A);
  
  // Text Colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFB3B3B3);
  static const Color textTertiary = Color(0xFF666666);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // Action Colors
  static const Color like = Color(0xFFFF3040);
  static const Color comment = Color(0xFFFFFFFF);
  static const Color share = Color(0xFFFFFFFF);
  static const Color bookmark = Color(0xFFFFD60A);
  
  // Status Colors
  static const Color success = Color(0xFF30D158);
  static const Color error = Color(0xFFFF453A);
  static const Color warning = Color(0xFFFF9F0A);
  static const Color info = Color(0xFF007AFF);
  
  // Commerce Colors
  static const Color price = Color(0xFF30D158);
  static const Color discount = Color(0xFFFF453A);
  static const Color addToCart = Color(0xFFFF006E);
  static const Color buyNow = Color(0xFF30D158);
  
  // Border and Divider
  static const Color border = Color(0xFF333333);
  static const Color divider = Color(0xFF2A2A2A);
  
  // Overlay
  static const Color overlay = Color(0x80000000);
  
  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, secondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, primary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [background, Color(0xFF1A1A1A)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
