class AppConstants {
  // App Info
  static const String appName = 'BeshrTok';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'https://api.beshrtok.com';
  static const String apiVersion = 'v1';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;
  
  // Video Configuration
  static const int maxVideoLength = 60; // seconds
  static const int minVideoLength = 3; // seconds
  static const double videoAspectRatio = 9 / 16; // TikTok style
  
  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // UI Constants
  static const double borderRadius = 12.0;
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusLarge = 16.0;
  
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  static const double marginSmall = 8.0;
  static const double marginMedium = 16.0;
  static const double marginLarge = 24.0;
  
  // Icon Sizes
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;
  
  // Button Sizes
  static const double buttonHeight = 48.0;
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightLarge = 56.0;
  
  // Bottom Navigation
  static const double bottomNavHeight = 80.0;
  static const double bottomNavIconSize = 28.0;
  
  // Action Button Sizes (TikTok style)
  static const double actionButtonSize = 48.0;
  static const double actionButtonIconSize = 24.0;
  static const double actionButtonSpacing = 16.0;
  
  // Product Card
  static const double productCardHeight = 200.0;
  static const double productImageHeight = 120.0;
  
  // Rating
  static const double ratingStarSize = 16.0;
  static const int maxRating = 5;
  
  // Cart
  static const int maxCartItems = 99;
  static const double minOrderAmount = 50.0;
  
  // Currency
  static const String currency = 'ر.س';
  static const String currencyCode = 'SAR';
  
  // Social Features
  static const int maxCommentLength = 500;
  static const int maxBioLength = 150;
  static const int maxUsernameLength = 30;
  
  // Cache
  static const Duration cacheExpiry = Duration(hours: 24);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  
  // Network
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  
  // Local Storage Keys
  static const String keyUser = 'user';
  static const String keyCart = 'cart';
  static const String keyWishlist = 'wishlist';
  static const String keySettings = 'settings';
  static const String keyTheme = 'theme';
  static const String keyLanguage = 'language';
  
  // Error Messages
  static const String errorNetwork = 'خطأ في الاتصال بالإنترنت';
  static const String errorGeneric = 'حدث خطأ غير متوقع';
  static const String errorAuth = 'خطأ في المصادقة';
  static const String errorNotFound = 'العنصر غير موجود';
  static const String errorServerError = 'خطأ في الخادم';
  
  // Success Messages
  static const String successAddedToCart = 'تم إضافة المنتج إلى السلة';
  static const String successRemovedFromCart = 'تم حذف المنتج من السلة';
  static const String successOrderPlaced = 'تم تأكيد الطلب بنجاح';
  static const String successProfileUpdated = 'تم تحديث الملف الشخصي';
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^[0-9]{10}$';
  
  // Social Media
  static const String instagramUrl = 'https://instagram.com/beshrtok';
  static const String twitterUrl = 'https://twitter.com/beshrtok';
  static const String facebookUrl = 'https://facebook.com/beshrtok';
  
  // App Store
  static const String appStoreUrl = 'https://apps.apple.com/app/beshrtok';
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.beshrtok.app';
}
