import 'package:flutter/material.dart';

class AppConstants {
  // App Info
  static const String appName = 'BeshrTok';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'https://api.beshrtok.com';
  static const String apiVersion = 'v1';
  static const Duration connectionTimeout = Duration(seconds: 30);
  
  // Dimensions
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  static const double borderRadius = 12.0;
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusLarge = 16.0;
  
  // Bottom Navigation
  static const double bottomNavHeight = 80.0;
  static const double bottomNavIconSize = 24.0;
  
  // Action Buttons
  static const double actionButtonSize = 48.0;
  static const double actionButtonIconSize = 24.0;
  static const double actionButtonSpacing = 16.0;
  
  // Animation Durations
  static const Duration fastAnimation = Duration(milliseconds: 150);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);
  
  // Local Storage Keys
  static const String keyUser = 'user';
  static const String keyCart = 'cart';
  static const String keyWishlist = 'wishlist';
  static const String keySettings = 'settings';
  static const String keyTheme = 'theme';
  static const String keyLanguage = 'language';
  
  // Messages
  static const String successAddedToCart = 'تم إضافة المنتج للسلة';
  static const String successRemovedFromCart = 'تم حذف المنتج من السلة';
  static const String successAddedToWishlist = 'تم إضافة المنتج لقائمة الأمنيات';
  static const String successRemovedFromWishlist = 'تم حذف المنتج من قائمة الأمنيات';
  
  static const String errorGeneral = 'حدث خطأ غير متوقع';
  static const String errorNetwork = 'خطأ في الاتصال بالإنترنت';
  static const String errorServer = 'خطأ في الخادم';
  static const String errorNotFound = 'العنصر غير موجود';
  static const String errorUnauthorized = 'غير مصرح لك بالوصول';
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxUsernameLength = 30;
  static const int maxBioLength = 500;
  static const int maxCommentLength = 500;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Upload
  static const int maxImageSizeMB = 10;
  static const int maxVideoSizeMB = 100;
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> allowedVideoFormats = ['mp4', 'mov', 'avi'];
  
  // Social Features
  static const int maxHashtags = 10;
  static const int maxMentions = 20;
  
  // Commerce
  static const double freeShippingThreshold = 200.0;
  static const double taxRate = 0.15; // 15% VAT
  static const String defaultCurrency = 'ر.س';
  
  // Video Player
  static const Duration videoSeekDuration = Duration(seconds: 10);
  static const double videoAspectRatio = 9 / 16;
  
  // Cache
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100; // MB
  
  // Notifications
  static const String notificationChannelId = 'beshrtok_notifications';
  static const String notificationChannelName = 'BeshrTok Notifications';
  static const String notificationChannelDescription = 'Notifications from BeshrTok app';
}
