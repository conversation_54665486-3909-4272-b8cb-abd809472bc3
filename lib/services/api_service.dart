import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/product.dart';
import '../models/user.dart';
import '../utils/constants.dart';

class ApiService {
  static const String _baseUrl = AppConstants.baseUrl;
  static const String _apiVersion = AppConstants.apiVersion;
  
  String? _authToken;
  
  // Headers
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    if (_authToken != null) 'Authorization': 'Bearer $_authToken',
  };

  // Set auth token
  void setAuthToken(String token) {
    _authToken = token;
  }

  // Clear auth token
  void clearAuthToken() {
    _authToken = null;
  }

  // Generic HTTP request method
  Future<Map<String, dynamic>?> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl/$_apiVersion/$endpoint');
      final uriWithParams = queryParams != null 
          ? uri.replace(queryParameters: queryParams)
          : uri;

      http.Response response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uriWithParams, headers: _headers)
              .timeout(AppConstants.connectionTimeout);
          break;
        case 'POST':
          response = await http.post(
            uriWithParams,
            headers: _headers,
            body: body != null ? json.encode(body) : null,
          ).timeout(AppConstants.connectionTimeout);
          break;
        case 'PUT':
          response = await http.put(
            uriWithParams,
            headers: _headers,
            body: body != null ? json.encode(body) : null,
          ).timeout(AppConstants.connectionTimeout);
          break;
        case 'DELETE':
          response = await http.delete(uriWithParams, headers: _headers)
              .timeout(AppConstants.connectionTimeout);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (response.body.isNotEmpty) {
          return json.decode(response.body);
        }
        return {};
      } else {
        debugPrint('API Error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Network Error: $e');
      return null;
    }
  }

  // Authentication APIs
  Future<User?> login(String email, String password) async {
    final response = await _makeRequest('POST', 'auth/login', body: {
      'email': email,
      'password': password,
    });

    if (response != null && response['success'] == true) {
      _authToken = response['data']['token'];
      return User.fromJson(response['data']['user']);
    }
    return null;
  }

  Future<User?> register({
    required String username,
    required String email,
    required String password,
    String? firstName,
    String? lastName,
    String? phone,
  }) async {
    final response = await _makeRequest('POST', 'auth/register', body: {
      'username': username,
      'email': email,
      'password': password,
      if (firstName != null) 'first_name': firstName,
      if (lastName != null) 'last_name': lastName,
      if (phone != null) 'phone': phone,
    });

    if (response != null && response['success'] == true) {
      _authToken = response['data']['token'];
      return User.fromJson(response['data']['user']);
    }
    return null;
  }

  Future<void> logout() async {
    await _makeRequest('POST', 'auth/logout');
    clearAuthToken();
  }

  Future<User?> getCurrentUser() async {
    final response = await _makeRequest('GET', 'auth/me');
    
    if (response != null && response['success'] == true) {
      return User.fromJson(response['data']);
    }
    return null;
  }

  // Products APIs
  Future<List<Product>> getForYouProducts({int page = 0, int limit = 20}) async {
    // For demo purposes, return mock data
    return _getMockProducts(page, limit, 'for_you');
  }

  Future<List<Product>> getStoreTokProducts({int page = 0, int limit = 20}) async {
    // For demo purposes, return mock data
    return _getMockProducts(page, limit, 'store_tok');
  }

  Future<List<Product>> getFollowingProducts({int page = 0, int limit = 20}) async {
    // For demo purposes, return mock data
    return _getMockProducts(page, limit, 'following');
  }

  Future<List<Product>> getDiscoverProducts({int page = 0, int limit = 20}) async {
    // For demo purposes, return mock data
    return _getMockProducts(page, limit, 'discover');
  }

  Future<List<Product>> getWishlistProducts() async {
    // For demo purposes, return mock data
    return _getMockProducts(0, 10, 'wishlist');
  }

  Future<List<Product>> searchProducts(String query) async {
    final response = await _makeRequest('GET', 'products/search', queryParams: {
      'q': query,
      'limit': '20',
    });

    if (response != null && response['success'] == true) {
      final List<dynamic> productsJson = response['data'];
      return productsJson.map((json) => Product.fromJson(json)).toList();
    }
    return [];
  }

  Future<Product?> getProductById(String productId) async {
    final response = await _makeRequest('GET', 'products/$productId');
    
    if (response != null && response['success'] == true) {
      return Product.fromJson(response['data']);
    }
    return null;
  }

  // Social APIs
  Future<bool> toggleLike(String productId) async {
    final response = await _makeRequest('POST', 'products/$productId/like');
    return response != null && response['success'] == true;
  }

  Future<bool> toggleBookmark(String productId) async {
    final response = await _makeRequest('POST', 'products/$productId/bookmark');
    return response != null && response['success'] == true;
  }

  // User APIs
  Future<User?> updateProfile({
    String? firstName,
    String? lastName,
    String? bio,
    String? phone,
    String? country,
    String? city,
    DateTime? birthDate,
    String? gender,
  }) async {
    final body = <String, dynamic>{};
    if (firstName != null) body['first_name'] = firstName;
    if (lastName != null) body['last_name'] = lastName;
    if (bio != null) body['bio'] = bio;
    if (phone != null) body['phone'] = phone;
    if (country != null) body['country'] = country;
    if (city != null) body['city'] = city;
    if (birthDate != null) body['birth_date'] = birthDate.toIso8601String();
    if (gender != null) body['gender'] = gender;

    final response = await _makeRequest('PUT', 'user/profile', body: body);
    
    if (response != null && response['success'] == true) {
      return User.fromJson(response['data']);
    }
    return null;
  }

  Future<String?> uploadAvatar(String imagePath) async {
    // For demo purposes, return a mock URL
    await Future.delayed(const Duration(seconds: 1));
    return 'https://example.com/avatars/user_${DateTime.now().millisecondsSinceEpoch}.jpg';
  }

  Future<bool> updatePreferences(UserPreferences preferences) async {
    final response = await _makeRequest('PUT', 'user/preferences', 
        body: preferences.toJson());
    return response != null && response['success'] == true;
  }

  Future<bool> followUser(String userId) async {
    final response = await _makeRequest('POST', 'users/$userId/follow');
    return response != null && response['success'] == true;
  }

  Future<bool> unfollowUser(String userId) async {
    final response = await _makeRequest('DELETE', 'users/$userId/follow');
    return response != null && response['success'] == true;
  }

  Future<bool> changePassword(String currentPassword, String newPassword) async {
    final response = await _makeRequest('PUT', 'user/password', body: {
      'current_password': currentPassword,
      'new_password': newPassword,
    });
    return response != null && response['success'] == true;
  }

  Future<bool> resetPassword(String email) async {
    final response = await _makeRequest('POST', 'auth/reset-password', body: {
      'email': email,
    });
    return response != null && response['success'] == true;
  }

  // Mock data for demo purposes
  List<Product> _getMockProducts(int page, int limit, String type) {
    final List<Product> products = [];
    final startIndex = page * limit;
    
    for (int i = startIndex; i < startIndex + limit; i++) {
      products.add(Product(
        id: '${type}_product_$i',
        title: 'منتج رائع ${i + 1}',
        description: 'وصف تفصيلي للمنتج ${i + 1} مع جميع المميزات والخصائص',
        price: 99.99 + (i * 10),
        originalPrice: i % 3 == 0 ? 149.99 + (i * 10) : null,
        currency: 'ر.س',
        images: [
          'https://picsum.photos/400/600?random=$i',
          'https://picsum.photos/400/600?random=${i + 100}',
        ],
        videoUrl: i % 4 == 0 ? 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4' : null,
        category: ['إلكترونيات', 'أزياء', 'منزل', 'رياضة'][i % 4],
        brand: ['براند A', 'براند B', 'براند C'][i % 3],
        rating: 4.0 + (i % 10) / 10,
        reviewCount: 50 + (i * 5),
        stockQuantity: 10 + (i % 20),
        isAvailable: true,
        isFeatured: i % 5 == 0,
        isOnSale: i % 3 == 0,
        discountPercentage: i % 3 == 0 ? 25.0 : null,
        tags: ['جديد', 'مميز', 'خصم'],
        attributes: {},
        createdAt: DateTime.now().subtract(Duration(days: i)),
        updatedAt: DateTime.now(),
        sellerId: 'seller_${i % 5}',
        sellerName: 'متجر ${i % 5 + 1}',
        sellerAvatar: 'https://picsum.photos/100/100?random=${i % 5}',
        likeCount: 100 + (i * 10),
        commentCount: 20 + (i * 2),
        shareCount: 5 + i,
        isLiked: i % 7 == 0,
        isBookmarked: i % 11 == 0,
      ));
    }
    
    return products;
  }
}
