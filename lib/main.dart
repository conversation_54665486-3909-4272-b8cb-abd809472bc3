import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/cart_provider.dart';
import 'providers/products_provider.dart';
import 'providers/user_provider.dart';
import 'screens/main_screen.dart';

void main() {
  runApp(const BeshrTokApp());
}

class BeshrTokApp extends StatelessWidget {
  const BeshrTokApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => ProductsProvider()),
        ChangeNotifierProvider(create: (_) => CartProvider()),
      ],
      child: const BeshrTokAppWrapper(),
    );
  }
}

class BeshrTokAppWrapper extends StatelessWidget {
  const BeshrTokAppWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'BeshrTok',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        brightness: Brightness.dark,
        fontFamily: 'Cairo', // You can add Arabic font later
      ),
      home: const MainScreen(),
    );
  }
}
