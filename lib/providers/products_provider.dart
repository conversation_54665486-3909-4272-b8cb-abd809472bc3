import 'package:flutter/foundation.dart';
import '../models/product.dart';
import '../services/api_service.dart';

class ProductsProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  List<Product> _forYouProducts = [];
  List<Product> _storeTokProducts = [];
  List<Product> _followingProducts = [];
  List<Product> _discoverProducts = [];
  List<Product> _wishlistProducts = [];
  
  bool _isLoadingForYou = false;
  bool _isLoadingStoreTok = false;
  bool _isLoadingFollowing = false;
  bool _isLoadingDiscover = false;
  bool _isLoadingWishlist = false;
  
  String? _error;
  int _currentForYouPage = 0;
  int _currentStoreTokPage = 0;
  int _currentFollowingPage = 0;
  int _currentDiscoverPage = 0;
  
  bool _hasMoreForYou = true;
  bool _hasMoreStoreTok = true;
  bool _hasMoreFollowing = true;
  bool _hasMoreDiscover = true;

  // Getters
  List<Product> get forYouProducts => _forYouProducts;
  List<Product> get storeTokProducts => _storeTokProducts;
  List<Product> get followingProducts => _followingProducts;
  List<Product> get discoverProducts => _discoverProducts;
  List<Product> get wishlistProducts => _wishlistProducts;
  
  bool get isLoadingForYou => _isLoadingForYou;
  bool get isLoadingStoreTok => _isLoadingStoreTok;
  bool get isLoadingFollowing => _isLoadingFollowing;
  bool get isLoadingDiscover => _isLoadingDiscover;
  bool get isLoadingWishlist => _isLoadingWishlist;
  
  String? get error => _error;
  
  bool get hasMoreForYou => _hasMoreForYou;
  bool get hasMoreStoreTok => _hasMoreStoreTok;
  bool get hasMoreFollowing => _hasMoreFollowing;
  bool get hasMoreDiscover => _hasMoreDiscover;

  // Load For You products
  Future<void> loadForYouProducts({bool refresh = false}) async {
    if (_isLoadingForYou) return;
    
    try {
      _isLoadingForYou = true;
      _error = null;
      
      if (refresh) {
        _currentForYouPage = 0;
        _hasMoreForYou = true;
        _forYouProducts.clear();
      }
      
      notifyListeners();

      final products = await _apiService.getForYouProducts(
        page: _currentForYouPage,
        limit: 20,
      );

      if (products.isEmpty) {
        _hasMoreForYou = false;
      } else {
        if (refresh) {
          _forYouProducts = products;
        } else {
          _forYouProducts.addAll(products);
        }
        _currentForYouPage++;
      }
      
    } catch (e) {
      _error = 'خطأ في تحميل المنتجات';
      debugPrint('Error loading for you products: $e');
    } finally {
      _isLoadingForYou = false;
      notifyListeners();
    }
  }

  // Load StoreTok products
  Future<void> loadStoreTokProducts({bool refresh = false}) async {
    if (_isLoadingStoreTok) return;
    
    try {
      _isLoadingStoreTok = true;
      _error = null;
      
      if (refresh) {
        _currentStoreTokPage = 0;
        _hasMoreStoreTok = true;
        _storeTokProducts.clear();
      }
      
      notifyListeners();

      final products = await _apiService.getStoreTokProducts(
        page: _currentStoreTokPage,
        limit: 20,
      );

      if (products.isEmpty) {
        _hasMoreStoreTok = false;
      } else {
        if (refresh) {
          _storeTokProducts = products;
        } else {
          _storeTokProducts.addAll(products);
        }
        _currentStoreTokPage++;
      }
      
    } catch (e) {
      _error = 'خطأ في تحميل منتجات المتجر';
      debugPrint('Error loading store tok products: $e');
    } finally {
      _isLoadingStoreTok = false;
      notifyListeners();
    }
  }

  // Load Following products
  Future<void> loadFollowingProducts({bool refresh = false}) async {
    if (_isLoadingFollowing) return;
    
    try {
      _isLoadingFollowing = true;
      _error = null;
      
      if (refresh) {
        _currentFollowingPage = 0;
        _hasMoreFollowing = true;
        _followingProducts.clear();
      }
      
      notifyListeners();

      final products = await _apiService.getFollowingProducts(
        page: _currentFollowingPage,
        limit: 20,
      );

      if (products.isEmpty) {
        _hasMoreFollowing = false;
      } else {
        if (refresh) {
          _followingProducts = products;
        } else {
          _followingProducts.addAll(products);
        }
        _currentFollowingPage++;
      }
      
    } catch (e) {
      _error = 'خطأ في تحميل منتجات المتابعين';
      debugPrint('Error loading following products: $e');
    } finally {
      _isLoadingFollowing = false;
      notifyListeners();
    }
  }

  // Load Discover products
  Future<void> loadDiscoverProducts({bool refresh = false}) async {
    if (_isLoadingDiscover) return;
    
    try {
      _isLoadingDiscover = true;
      _error = null;
      
      if (refresh) {
        _currentDiscoverPage = 0;
        _hasMoreDiscover = true;
        _discoverProducts.clear();
      }
      
      notifyListeners();

      final products = await _apiService.getDiscoverProducts(
        page: _currentDiscoverPage,
        limit: 20,
      );

      if (products.isEmpty) {
        _hasMoreDiscover = false;
      } else {
        if (refresh) {
          _discoverProducts = products;
        } else {
          _discoverProducts.addAll(products);
        }
        _currentDiscoverPage++;
      }
      
    } catch (e) {
      _error = 'خطأ في تحميل منتجات الاستكشاف';
      debugPrint('Error loading discover products: $e');
    } finally {
      _isLoadingDiscover = false;
      notifyListeners();
    }
  }

  // Load Wishlist products
  Future<void> loadWishlistProducts() async {
    if (_isLoadingWishlist) return;
    
    try {
      _isLoadingWishlist = true;
      _error = null;
      notifyListeners();

      final products = await _apiService.getWishlistProducts();
      _wishlistProducts = products;
      
    } catch (e) {
      _error = 'خطأ في تحميل قائمة الأمنيات';
      debugPrint('Error loading wishlist products: $e');
    } finally {
      _isLoadingWishlist = false;
      notifyListeners();
    }
  }

  // Toggle like
  Future<void> toggleLike(String productId) async {
    try {
      // Update UI immediately for better UX
      _updateProductLike(productId);
      
      // Make API call
      await _apiService.toggleLike(productId);
      
    } catch (e) {
      // Revert UI change if API call fails
      _updateProductLike(productId);
      _error = 'خطأ في تحديث الإعجاب';
      debugPrint('Error toggling like: $e');
      notifyListeners();
    }
  }

  // Toggle bookmark
  Future<void> toggleBookmark(String productId) async {
    try {
      // Update UI immediately for better UX
      _updateProductBookmark(productId);
      
      // Make API call
      await _apiService.toggleBookmark(productId);
      
    } catch (e) {
      // Revert UI change if API call fails
      _updateProductBookmark(productId);
      _error = 'خطأ في تحديث الحفظ';
      debugPrint('Error toggling bookmark: $e');
      notifyListeners();
    }
  }

  // Search products
  Future<List<Product>> searchProducts(String query) async {
    try {
      return await _apiService.searchProducts(query);
    } catch (e) {
      _error = 'خطأ في البحث';
      debugPrint('Error searching products: $e');
      notifyListeners();
      return [];
    }
  }

  // Get product by ID
  Future<Product?> getProductById(String productId) async {
    try {
      return await _apiService.getProductById(productId);
    } catch (e) {
      _error = 'خطأ في تحميل المنتج';
      debugPrint('Error getting product: $e');
      notifyListeners();
      return null;
    }
  }

  // Helper methods
  void _updateProductLike(String productId) {
    _updateProductInLists(productId, (product) {
      return product.copyWith(
        isLiked: !product.isLiked,
        likeCount: product.isLiked ? product.likeCount - 1 : product.likeCount + 1,
      );
    });
  }

  void _updateProductBookmark(String productId) {
    _updateProductInLists(productId, (product) {
      return product.copyWith(isBookmarked: !product.isBookmarked);
    });
  }

  void _updateProductInLists(String productId, Product Function(Product) updateFunction) {
    // Update in all lists
    _forYouProducts = _updateProductInList(_forYouProducts, productId, updateFunction);
    _storeTokProducts = _updateProductInList(_storeTokProducts, productId, updateFunction);
    _followingProducts = _updateProductInList(_followingProducts, productId, updateFunction);
    _discoverProducts = _updateProductInList(_discoverProducts, productId, updateFunction);
    _wishlistProducts = _updateProductInList(_wishlistProducts, productId, updateFunction);
    
    notifyListeners();
  }

  List<Product> _updateProductInList(
    List<Product> products, 
    String productId, 
    Product Function(Product) updateFunction
  ) {
    return products.map((product) {
      if (product.id == productId) {
        return updateFunction(product);
      }
      return product;
    }).toList();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void refresh() {
    loadForYouProducts(refresh: true);
    loadStoreTokProducts(refresh: true);
    loadFollowingProducts(refresh: true);
    loadDiscoverProducts(refresh: true);
  }
}
