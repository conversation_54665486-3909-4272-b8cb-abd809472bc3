import 'package:flutter/foundation.dart';

class ProductsProvider with ChangeNotifier {
  List<Map<String, dynamic>> _forYouProducts = [];
  List<Map<String, dynamic>> _storeTokProducts = [];
  List<Map<String, dynamic>> _followingProducts = [];
  List<Map<String, dynamic>> _discoverProducts = [];
  
  bool _isLoadingForYou = false;
  bool _isLoadingStoreTok = false;
  bool _isLoadingFollowing = false;
  bool _isLoadingDiscover = false;
  
  String? _error;

  // Getters
  List<Map<String, dynamic>> get forYouProducts => _forYouProducts;
  List<Map<String, dynamic>> get storeTokProducts => _storeTokProducts;
  List<Map<String, dynamic>> get followingProducts => _followingProducts;
  List<Map<String, dynamic>> get discoverProducts => _discoverProducts;
  
  bool get isLoadingForYou => _isLoadingForYou;
  bool get isLoadingStoreTok => _isLoadingStoreTok;
  bool get isLoadingFollowing => _isLoadingFollowing;
  bool get isLoadingDiscover => _isLoadingDiscover;
  
  String? get error => _error;

  // Load For You products
  Future<void> loadForYouProducts({bool refresh = false}) async {
    if (_isLoadingForYou) return;
    
    try {
      _isLoadingForYou = true;
      _error = null;
      
      if (refresh) {
        _forYouProducts.clear();
      }
      
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      final products = _generateMockProducts('for_you');
      
      if (refresh) {
        _forYouProducts = products;
      } else {
        _forYouProducts.addAll(products);
      }
      
    } catch (e) {
      _error = 'خطأ في تحميل المنتجات';
      debugPrint('Error loading for you products: $e');
    } finally {
      _isLoadingForYou = false;
      notifyListeners();
    }
  }

  // Load StoreTok products
  Future<void> loadStoreTokProducts({bool refresh = false}) async {
    if (_isLoadingStoreTok) return;
    
    try {
      _isLoadingStoreTok = true;
      _error = null;
      
      if (refresh) {
        _storeTokProducts.clear();
      }
      
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      final products = _generateMockProducts('store_tok');
      
      if (refresh) {
        _storeTokProducts = products;
      } else {
        _storeTokProducts.addAll(products);
      }
      
    } catch (e) {
      _error = 'خطأ في تحميل منتجات المتجر';
      debugPrint('Error loading store tok products: $e');
    } finally {
      _isLoadingStoreTok = false;
      notifyListeners();
    }
  }

  // Load Following products
  Future<void> loadFollowingProducts({bool refresh = false}) async {
    if (_isLoadingFollowing) return;
    
    try {
      _isLoadingFollowing = true;
      _error = null;
      
      if (refresh) {
        _followingProducts.clear();
      }
      
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      final products = _generateMockProducts('following');
      
      if (refresh) {
        _followingProducts = products;
      } else {
        _followingProducts.addAll(products);
      }
      
    } catch (e) {
      _error = 'خطأ في تحميل منتجات المتابعين';
      debugPrint('Error loading following products: $e');
    } finally {
      _isLoadingFollowing = false;
      notifyListeners();
    }
  }

  // Load Discover products
  Future<void> loadDiscoverProducts({bool refresh = false}) async {
    if (_isLoadingDiscover) return;
    
    try {
      _isLoadingDiscover = true;
      _error = null;
      
      if (refresh) {
        _discoverProducts.clear();
      }
      
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      final products = _generateMockProducts('discover');
      
      if (refresh) {
        _discoverProducts = products;
      } else {
        _discoverProducts.addAll(products);
      }
      
    } catch (e) {
      _error = 'خطأ في تحميل منتجات الاستكشاف';
      debugPrint('Error loading discover products: $e');
    } finally {
      _isLoadingDiscover = false;
      notifyListeners();
    }
  }

  // Generate mock products
  List<Map<String, dynamic>> _generateMockProducts(String type) {
    return List.generate(10, (index) {
      return {
        'id': '${type}_$index',
        'title': 'منتج رائع ${index + 1}',
        'description': 'وصف تفصيلي للمنتج ${index + 1}',
        'price': 99.99 + (index * 10),
        'originalPrice': index % 3 == 0 ? 149.99 + (index * 10) : null,
        'image': 'https://picsum.photos/400/600?random=$index',
        'video': index % 4 == 0 ? 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4' : null,
        'category': ['إلكترونيات', 'أزياء', 'منزل', 'رياضة'][index % 4],
        'brand': ['براند A', 'براند B', 'براند C'][index % 3],
        'rating': 4.0 + (index % 10) / 10,
        'reviewCount': 50 + (index * 5),
        'isLiked': index % 7 == 0,
        'isBookmarked': index % 11 == 0,
        'likeCount': 100 + (index * 10),
        'commentCount': 20 + (index * 2),
        'shareCount': 5 + index,
        'sellerName': 'متجر ${index % 5 + 1}',
        'sellerAvatar': 'https://picsum.photos/100/100?random=${index % 5}',
        'hasDiscount': index % 3 == 0,
        'discountPercentage': index % 3 == 0 ? 25.0 : null,
      };
    });
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
