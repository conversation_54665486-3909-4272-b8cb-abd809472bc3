import 'package:flutter/foundation.dart';

class UserProvider with ChangeNotifier {
  Map<String, dynamic>? _currentUser;
  bool _isLoading = false;
  bool _isAuthenticated = false;
  String? _error;

  // Getters
  Map<String, dynamic>? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  String? get error => _error;
  
  bool get hasUser => _currentUser != null;
  String get displayName => _currentUser?['displayName'] ?? 'مستخدم';
  String get username => _currentUser?['username'] ?? '';
  String? get avatar => _currentUser?['avatar'];

  // Login
  Future<bool> login(String email, String password) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock successful login
      _currentUser = {
        'id': 'user_123',
        'username': 'user123',
        'displayName': 'أحمد محمد',
        'email': email,
        'avatar': null,
        'bio': 'مرحباً بكم في ملفي الشخصي',
        'followersCount': 1250,
        'followingCount': 180,
        'postsCount': 45,
        'likesCount': 5600,
        'isVerified': false,
      };
      _isAuthenticated = true;
      
      return true;
      
    } catch (e) {
      _error = 'خطأ في تسجيل الدخول';
      debugPrint('Error logging in: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Register
  Future<bool> register({
    required String username,
    required String email,
    required String password,
    String? firstName,
    String? lastName,
    String? phone,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock successful registration
      _currentUser = {
        'id': 'user_${DateTime.now().millisecondsSinceEpoch}',
        'username': username,
        'displayName': '${firstName ?? ''} ${lastName ?? ''}',
        'email': email,
        'avatar': null,
        'bio': null,
        'followersCount': 0,
        'followingCount': 0,
        'postsCount': 0,
        'likesCount': 0,
        'isVerified': false,
      };
      _isAuthenticated = true;
      
      return true;
      
    } catch (e) {
      _error = 'خطأ في إنشاء الحساب';
      debugPrint('Error registering: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      _isLoading = true;
      notifyListeners();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      _currentUser = null;
      _isAuthenticated = false;
      
    } catch (e) {
      _error = 'خطأ في تسجيل الخروج';
      debugPrint('Error logging out: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
