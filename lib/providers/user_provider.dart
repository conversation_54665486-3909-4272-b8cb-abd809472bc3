import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/user.dart';
import '../services/api_service.dart';
import '../utils/constants.dart';

class UserProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  User? _currentUser;
  bool _isLoading = false;
  bool _isAuthenticated = false;
  String? _error;

  // Getters
  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  String? get error => _error;
  
  bool get hasUser => _currentUser != null;
  String get displayName => _currentUser?.displayName ?? 'مستخدم';
  String get username => _currentUser?.username ?? '';
  String? get avatar => _currentUser?.avatar;

  UserProvider() {
    _loadUserFromStorage();
  }

  // Load user from local storage
  Future<void> _loadUserFromStorage() async {
    try {
      _isLoading = true;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(AppConstants.keyUser);
      
      if (userJson != null) {
        final userData = json.decode(userJson);
        _currentUser = User.fromJson(userData);
        _isAuthenticated = true;
        
        // Refresh user data from server
        await _refreshUserData();
      }
    } catch (e) {
      _error = 'خطأ في تحميل بيانات المستخدم';
      debugPrint('Error loading user: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Save user to local storage
  Future<void> _saveUserToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_currentUser != null) {
        final userJson = json.encode(_currentUser!.toJson());
        await prefs.setString(AppConstants.keyUser, userJson);
      } else {
        await prefs.remove(AppConstants.keyUser);
      }
    } catch (e) {
      debugPrint('Error saving user: $e');
    }
  }

  // Refresh user data from server
  Future<void> _refreshUserData() async {
    if (_currentUser == null) return;
    
    try {
      final updatedUser = await _apiService.getCurrentUser();
      if (updatedUser != null) {
        _currentUser = updatedUser;
        await _saveUserToStorage();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error refreshing user data: $e');
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final user = await _apiService.login(email, password);
      
      if (user != null) {
        _currentUser = user;
        _isAuthenticated = true;
        await _saveUserToStorage();
        return true;
      } else {
        _error = 'بيانات الدخول غير صحيحة';
        return false;
      }
      
    } catch (e) {
      _error = 'خطأ في تسجيل الدخول';
      debugPrint('Error logging in: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Register
  Future<bool> register({
    required String username,
    required String email,
    required String password,
    String? firstName,
    String? lastName,
    String? phone,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final user = await _apiService.register(
        username: username,
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );
      
      if (user != null) {
        _currentUser = user;
        _isAuthenticated = true;
        await _saveUserToStorage();
        return true;
      } else {
        _error = 'خطأ في إنشاء الحساب';
        return false;
      }
      
    } catch (e) {
      _error = 'خطأ في إنشاء الحساب';
      debugPrint('Error registering: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      _isLoading = true;
      notifyListeners();

      await _apiService.logout();
      
      _currentUser = null;
      _isAuthenticated = false;
      await _saveUserToStorage();
      
      // Clear other stored data
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.keyCart);
      await prefs.remove(AppConstants.keyWishlist);
      
    } catch (e) {
      _error = 'خطأ في تسجيل الخروج';
      debugPrint('Error logging out: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update profile
  Future<bool> updateProfile({
    String? firstName,
    String? lastName,
    String? bio,
    String? phone,
    String? country,
    String? city,
    DateTime? birthDate,
    String? gender,
  }) async {
    if (_currentUser == null) return false;
    
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final updatedUser = await _apiService.updateProfile(
        firstName: firstName,
        lastName: lastName,
        bio: bio,
        phone: phone,
        country: country,
        city: city,
        birthDate: birthDate,
        gender: gender,
      );
      
      if (updatedUser != null) {
        _currentUser = updatedUser;
        await _saveUserToStorage();
        return true;
      } else {
        _error = 'خطأ في تحديث الملف الشخصي';
        return false;
      }
      
    } catch (e) {
      _error = 'خطأ في تحديث الملف الشخصي';
      debugPrint('Error updating profile: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update avatar
  Future<bool> updateAvatar(String imagePath) async {
    if (_currentUser == null) return false;
    
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final avatarUrl = await _apiService.uploadAvatar(imagePath);
      
      if (avatarUrl != null) {
        _currentUser = _currentUser!.copyWith(avatar: avatarUrl);
        await _saveUserToStorage();
        return true;
      } else {
        _error = 'خطأ في تحديث الصورة الشخصية';
        return false;
      }
      
    } catch (e) {
      _error = 'خطأ في تحديث الصورة الشخصية';
      debugPrint('Error updating avatar: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update preferences
  Future<bool> updatePreferences(UserPreferences preferences) async {
    if (_currentUser == null) return false;
    
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final success = await _apiService.updatePreferences(preferences);
      
      if (success) {
        _currentUser = _currentUser!.copyWith(preferences: preferences);
        await _saveUserToStorage();
        return true;
      } else {
        _error = 'خطأ في تحديث الإعدادات';
        return false;
      }
      
    } catch (e) {
      _error = 'خطأ في تحديث الإعدادات';
      debugPrint('Error updating preferences: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Follow user
  Future<bool> followUser(String userId) async {
    try {
      final success = await _apiService.followUser(userId);
      
      if (success && _currentUser != null) {
        _currentUser = _currentUser!.copyWith(
          followingCount: _currentUser!.followingCount + 1,
        );
        await _saveUserToStorage();
        notifyListeners();
      }
      
      return success;
    } catch (e) {
      _error = 'خطأ في متابعة المستخدم';
      debugPrint('Error following user: $e');
      notifyListeners();
      return false;
    }
  }

  // Unfollow user
  Future<bool> unfollowUser(String userId) async {
    try {
      final success = await _apiService.unfollowUser(userId);
      
      if (success && _currentUser != null) {
        _currentUser = _currentUser!.copyWith(
          followingCount: _currentUser!.followingCount - 1,
        );
        await _saveUserToStorage();
        notifyListeners();
      }
      
      return success;
    } catch (e) {
      _error = 'خطأ في إلغاء متابعة المستخدم';
      debugPrint('Error unfollowing user: $e');
      notifyListeners();
      return false;
    }
  }

  // Change password
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final success = await _apiService.changePassword(currentPassword, newPassword);
      
      if (!success) {
        _error = 'خطأ في تغيير كلمة المرور';
      }
      
      return success;
      
    } catch (e) {
      _error = 'خطأ في تغيير كلمة المرور';
      debugPrint('Error changing password: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Reset password
  Future<bool> resetPassword(String email) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final success = await _apiService.resetPassword(email);
      
      if (!success) {
        _error = 'خطأ في إرسال رابط إعادة تعيين كلمة المرور';
      }
      
      return success;
      
    } catch (e) {
      _error = 'خطأ في إرسال رابط إعادة تعيين كلمة المرور';
      debugPrint('Error resetting password: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Refresh user data
  Future<void> refresh() async {
    await _refreshUserData();
  }
}
