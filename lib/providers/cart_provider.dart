import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/cart_item.dart';
import '../models/product.dart';
import '../utils/constants.dart';

class CartProvider with ChangeNotifier {
  Cart _cart = Cart.empty();
  bool _isLoading = false;
  String? _error;

  Cart get cart => _cart;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  int get itemCount => _cart.itemCount;
  int get totalQuantity => _cart.totalQuantity;
  double get subtotal => _cart.subtotal;
  double get total => _cart.total;
  bool get isEmpty => _cart.isEmpty;
  bool get isNotEmpty => _cart.isNotEmpty;

  CartProvider() {
    _loadCartFromStorage();
  }

  Future<void> _loadCartFromStorage() async {
    try {
      _isLoading = true;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();
      final cartJson = prefs.getString(AppConstants.keyCart);
      
      if (cartJson != null) {
        final cartData = json.decode(cartJson);
        _cart = Cart.fromJson(cartData);
      }
    } catch (e) {
      _error = 'خطأ في تحميل السلة';
      debugPrint('Error loading cart: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _saveCartToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartJson = json.encode(_cart.toJson());
      await prefs.setString(AppConstants.keyCart, cartJson);
    } catch (e) {
      debugPrint('Error saving cart: $e');
    }
  }

  Future<void> addToCart(Product product, {int quantity = 1, String? variant, Map<String, dynamic>? options}) async {
    try {
      _error = null;
      
      // Check if product is available
      if (!product.isInStock) {
        _error = 'المنتج غير متوفر حالياً';
        notifyListeners();
        return;
      }

      // Check if item already exists in cart
      final existingItemIndex = _cart.items.indexWhere((item) => 
        item.product.id == product.id && 
        item.selectedVariant == variant
      );

      List<CartItem> updatedItems = List.from(_cart.items);

      if (existingItemIndex != -1) {
        // Update existing item quantity
        final existingItem = updatedItems[existingItemIndex];
        final newQuantity = existingItem.quantity + quantity;
        
        if (newQuantity > product.stockQuantity) {
          _error = 'الكمية المطلوبة غير متوفرة';
          notifyListeners();
          return;
        }

        updatedItems[existingItemIndex] = existingItem.copyWith(
          quantity: newQuantity,
          totalPrice: existingItem.unitPrice * newQuantity,
        );
      } else {
        // Add new item
        if (quantity > product.stockQuantity) {
          _error = 'الكمية المطلوبة غير متوفرة';
          notifyListeners();
          return;
        }

        final cartItem = CartItem(
          id: '${product.id}_${DateTime.now().millisecondsSinceEpoch}',
          product: product,
          quantity: quantity,
          selectedVariant: variant,
          selectedOptions: options,
          addedAt: DateTime.now(),
          unitPrice: product.price,
          totalPrice: product.price * quantity,
        );

        updatedItems.add(cartItem);
      }

      _updateCart(updatedItems);
      await _saveCartToStorage();
      
    } catch (e) {
      _error = 'خطأ في إضافة المنتج للسلة';
      debugPrint('Error adding to cart: $e');
      notifyListeners();
    }
  }

  Future<void> removeFromCart(String itemId) async {
    try {
      _error = null;
      
      final updatedItems = _cart.items.where((item) => item.id != itemId).toList();
      _updateCart(updatedItems);
      await _saveCartToStorage();
      
    } catch (e) {
      _error = 'خطأ في حذف المنتج من السلة';
      debugPrint('Error removing from cart: $e');
      notifyListeners();
    }
  }

  Future<void> updateQuantity(String itemId, int quantity) async {
    try {
      _error = null;
      
      if (quantity <= 0) {
        await removeFromCart(itemId);
        return;
      }

      final itemIndex = _cart.items.indexWhere((item) => item.id == itemId);
      if (itemIndex == -1) return;

      final item = _cart.items[itemIndex];
      
      if (quantity > item.product.stockQuantity) {
        _error = 'الكمية المطلوبة غير متوفرة';
        notifyListeners();
        return;
      }

      List<CartItem> updatedItems = List.from(_cart.items);
      updatedItems[itemIndex] = item.copyWith(
        quantity: quantity,
        totalPrice: item.unitPrice * quantity,
      );

      _updateCart(updatedItems);
      await _saveCartToStorage();
      
    } catch (e) {
      _error = 'خطأ في تحديث الكمية';
      debugPrint('Error updating quantity: $e');
      notifyListeners();
    }
  }

  Future<void> clearCart() async {
    try {
      _error = null;
      _cart = Cart.empty();
      await _saveCartToStorage();
      notifyListeners();
    } catch (e) {
      _error = 'خطأ في مسح السلة';
      debugPrint('Error clearing cart: $e');
      notifyListeners();
    }
  }

  Future<void> applyCoupon(String couponCode) async {
    try {
      _error = null;
      _isLoading = true;
      notifyListeners();

      // Simulate API call to validate coupon
      await Future.delayed(const Duration(seconds: 1));
      
      // For demo purposes, apply 10% discount for "SAVE10" coupon
      if (couponCode.toUpperCase() == 'SAVE10') {
        final discount = _cart.subtotal * 0.1;
        _cart = _cart.copyWith(
          couponCode: couponCode,
          discount: discount,
          total: _cart.subtotal + _cart.tax + _cart.shipping - discount,
        );
        await _saveCartToStorage();
      } else {
        _error = 'كود الخصم غير صحيح';
      }
      
    } catch (e) {
      _error = 'خطأ في تطبيق كود الخصم';
      debugPrint('Error applying coupon: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> removeCoupon() async {
    try {
      _error = null;
      _cart = _cart.copyWith(
        couponCode: null,
        discount: 0,
        total: _cart.subtotal + _cart.tax + _cart.shipping,
      );
      await _saveCartToStorage();
      notifyListeners();
    } catch (e) {
      _error = 'خطأ في إزالة كود الخصم';
      debugPrint('Error removing coupon: $e');
      notifyListeners();
    }
  }

  void _updateCart(List<CartItem> items) {
    final subtotal = items.fold(0.0, (sum, item) => sum + item.totalPrice);
    final tax = subtotal * 0.15; // 15% VAT
    final shipping = subtotal > 200 ? 0.0 : 25.0; // Free shipping over 200 SAR
    final total = subtotal + tax + shipping - _cart.discount;

    _cart = _cart.copyWith(
      items: items,
      subtotal: subtotal,
      tax: tax,
      shipping: shipping,
      total: total,
      updatedAt: DateTime.now(),
    );
    
    notifyListeners();
  }

  bool isInCart(String productId, {String? variant}) {
    return _cart.items.any((item) => 
      item.product.id == productId && 
      item.selectedVariant == variant
    );
  }

  CartItem? getCartItem(String productId, {String? variant}) {
    try {
      return _cart.items.firstWhere((item) => 
        item.product.id == productId && 
        item.selectedVariant == variant
      );
    } catch (e) {
      return null;
    }
  }

  int getProductQuantity(String productId, {String? variant}) {
    final item = getCartItem(productId, variant: variant);
    return item?.quantity ?? 0;
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
