import 'package:flutter/foundation.dart';

class CartProvider with ChangeNotifier {
  List<Map<String, dynamic>> _items = [];
  bool _isLoading = false;
  String? _error;

  List<Map<String, dynamic>> get items => _items;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  int get itemCount => _items.length;
  int get totalQuantity => _items.fold(0, (sum, item) => sum + (item['quantity'] as int));
  double get subtotal => _items.fold(0.0, (sum, item) => sum + (item['totalPrice'] as double));
  double get total => subtotal;
  bool get isEmpty => _items.isEmpty;
  bool get isNotEmpty => _items.isNotEmpty;

  void addToCart(Map<String, dynamic> product, {int quantity = 1}) {
    try {
      _error = null;
      
      // Check if item already exists
      final existingIndex = _items.indexWhere((item) => item['id'] == product['id']);
      
      if (existingIndex != -1) {
        // Update existing item
        _items[existingIndex]['quantity'] += quantity;
        _items[existingIndex]['totalPrice'] = _items[existingIndex]['quantity'] * _items[existingIndex]['price'];
      } else {
        // Add new item
        _items.add({
          'id': product['id'],
          'title': product['title'],
          'price': product['price'],
          'image': product['image'],
          'quantity': quantity,
          'totalPrice': (product['price'] as double) * quantity,
        });
      }
      
      notifyListeners();
    } catch (e) {
      _error = 'خطأ في إضافة المنتج للسلة';
      notifyListeners();
    }
  }

  void removeFromCart(String itemId) {
    try {
      _error = null;
      _items.removeWhere((item) => item['id'] == itemId);
      notifyListeners();
    } catch (e) {
      _error = 'خطأ في حذف المنتج من السلة';
      notifyListeners();
    }
  }

  void updateQuantity(String itemId, int quantity) {
    try {
      _error = null;
      
      if (quantity <= 0) {
        removeFromCart(itemId);
        return;
      }

      final itemIndex = _items.indexWhere((item) => item['id'] == itemId);
      if (itemIndex != -1) {
        _items[itemIndex]['quantity'] = quantity;
        _items[itemIndex]['totalPrice'] = _items[itemIndex]['price'] * quantity;
        notifyListeners();
      }
    } catch (e) {
      _error = 'خطأ في تحديث الكمية';
      notifyListeners();
    }
  }

  void clearCart() {
    _items.clear();
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
